<template>
  <div class="code-editor-container">
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button size="small" @click="formatCode">
            <el-icon><Document /></el-icon>
            格式化
          </el-button>
          <el-button size="small" @click="validateSyntax">
            <el-icon><Check /></el-icon>
            语法检查
          </el-button>
          <el-button size="small" @click="showHelp">
            <el-icon><QuestionFilled /></el-icon>
            帮助
          </el-button>
        </el-button-group>
      </div>
      <div class="toolbar-right">
        <el-select v-model="selectedTemplate" placeholder="选择模板" size="small" @change="loadTemplate">
          <el-option label="空白脚本" value="blank" />
          <el-option label="抖音点赞" value="douyin_like" />
          <el-option label="抖音关注" value="douyin_follow" />
          <el-option label="抖音评论" value="douyin_comment" />
          <el-option label="设备控制" value="device_control" />
        </el-select>
        <el-button size="small" type="primary" @click="runScript" :loading="running">
          <el-icon><VideoPlay /></el-icon>
          运行
        </el-button>
      </div>
    </div>
    
    <div class="editor-main">
      <div class="editor-wrapper" ref="editorContainer">
        <textarea
          ref="codeTextarea"
          v-model="code"
          class="code-textarea"
          :placeholder="placeholder"
          @input="onCodeChange"
          @keydown="onKeyDown"
          spellcheck="false"
        ></textarea>
        <div class="line-numbers" ref="lineNumbers"></div>
        <div class="syntax-highlights" ref="syntaxHighlights"></div>
      </div>
      
      <div class="editor-sidebar">
        <el-tabs v-model="activeTab" class="sidebar-tabs">
          <el-tab-pane label="函数库" name="functions">
            <div class="function-list">
              <div class="function-category" v-for="category in functionCategories" :key="category.name">
                <h4 class="category-title">{{ category.name }}</h4>
                <div class="function-item" 
                     v-for="func in category.functions" 
                     :key="func.name"
                     @click="insertFunction(func)"
                >
                  <div class="function-name">{{ func.name }}</div>
                  <div class="function-desc">{{ func.description }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="变量" name="variables">
            <div class="variable-list">
              <div class="variable-item" v-for="variable in availableVariables" :key="variable.name">
                <div class="variable-name">{{ variable.name }}</div>
                <div class="variable-type">{{ variable.type }}</div>
                <div class="variable-desc">{{ variable.description }}</div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="错误" name="errors">
            <div class="error-list">
              <div class="error-item" 
                   v-for="error in syntaxErrors" 
                   :key="error.line"
                   @click="goToLine(error.line)"
              >
                <el-icon class="error-icon"><WarningFilled /></el-icon>
                <div class="error-content">
                  <div class="error-message">{{ error.message }}</div>
                  <div class="error-location">第 {{ error.line }} 行</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    
    <div class="editor-status">
      <div class="status-left">
        <span class="status-item">行: {{ currentLine }}</span>
        <span class="status-item">列: {{ currentColumn }}</span>
        <span class="status-item">字符: {{ code.length }}</span>
      </div>
      <div class="status-right">
        <el-tag :type="syntaxValid ? 'success' : 'danger'" size="small">
          {{ syntaxValid ? '语法正确' : '语法错误' }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Document, Check, QuestionFilled, VideoPlay, WarningFilled } from '@element-plus/icons-vue';

export default {
  name: 'CodeEditor',
  components: {
    Document,
    Check,
    QuestionFilled,
    VideoPlay,
    WarningFilled
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'python'
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'run'],
  setup(props, { emit }) {
    const code = ref(props.modelValue);
    const codeTextarea = ref(null);
    const lineNumbers = ref(null);
    const syntaxHighlights = ref(null);
    const editorContainer = ref(null);
    
    const activeTab = ref('functions');
    const selectedTemplate = ref('');
    const running = ref(false);
    const currentLine = ref(1);
    const currentColumn = ref(1);
    const syntaxErrors = ref([]);
    const syntaxValid = computed(() => syntaxErrors.value.length === 0);
    
    const placeholder = `# 编写你的自动化脚本
# 可用的全局变量:
# - device_ids: 目标设备ID列表
# - log_info(message): 记录信息日志
# - log_success(message): 记录成功日志
# - log_warning(message): 记录警告日志
# - log_error(message): 记录错误日志

# 示例脚本:
for device_id in device_ids:
    log_info(f"开始在设备 {device_id} 上执行脚本")
    
    # 打开抖音
    open_app(device_id, "com.ss.android.ugc.aweme")
    time.sleep(3)
    
    # 点赞
    click_phone(device_id, 664, 900, "点赞")
    log_success(f"设备 {device_id} 点赞成功")`;

    // 函数库
    const functionCategories = ref([
      {
        name: '设备控制',
        functions: [
          {
            name: 'click_phone(device_id, x, y, action_name)',
            description: '点击屏幕指定坐标',
            template: 'click_phone(device_id, ${1:x}, ${2:y}, "${3:action_name}")'
          },
          {
            name: 'touch_swip(device_id, x1, y1, x2, y2, duration)',
            description: '滑动操作',
            template: 'touch_swip(device_id, ${1:x1}, ${2:y1}, ${3:x2}, ${4:y2}, ${5:duration})'
          },
          {
            name: 'input_text(device_id, text)',
            description: '输入文本',
            template: 'input_text(device_id, "${1:text}")'
          },
          {
            name: 'open_app(device_id, package_name)',
            description: '打开应用',
            template: 'open_app(device_id, "${1:package_name}")'
          }
        ]
      },
      {
        name: '抖音操作',
        functions: [
          {
            name: 'douyin_like(device_id)',
            description: '抖音点赞',
            template: 'douyin_like(device_id)'
          },
          {
            name: 'douyin_follow(device_id)',
            description: '抖音关注',
            template: 'douyin_follow(device_id)'
          },
          {
            name: 'douyin_collect(device_id)',
            description: '抖音收藏',
            template: 'douyin_collect(device_id)'
          },
          {
            name: 'douyin_comment(device_id, text)',
            description: '抖音评论',
            template: 'douyin_comment(device_id, "${1:comment_text}")'
          }
        ]
      },
      {
        name: '日志记录',
        functions: [
          {
            name: 'log_info(message)',
            description: '记录信息日志',
            template: 'log_info("${1:message}")'
          },
          {
            name: 'log_success(message)',
            description: '记录成功日志',
            template: 'log_success("${1:message}")'
          },
          {
            name: 'log_warning(message)',
            description: '记录警告日志',
            template: 'log_warning("${1:message}")'
          },
          {
            name: 'log_error(message)',
            description: '记录错误日志',
            template: 'log_error("${1:message}")'
          }
        ]
      }
    ]);

    // 可用变量
    const availableVariables = ref([
      {
        name: 'device_ids',
        type: 'List[str]',
        description: '目标设备ID列表'
      },
      {
        name: 'time',
        type: 'module',
        description: 'Python时间模块'
      },
      {
        name: 'random',
        type: 'module',
        description: 'Python随机数模块'
      },
      {
        name: 'json',
        type: 'module',
        description: 'Python JSON模块'
      }
    ]);

    // 脚本模板
    const templates = {
      blank: '',
      douyin_like: `# 抖音自动点赞脚本
import time
import random

for device_id in device_ids:
    log_info(f"开始在设备 {device_id} 上执行点赞脚本")
    
    # 打开抖音
    open_app(device_id, "com.ss.android.ugc.aweme")
    time.sleep(3)
    
    # 循环点赞10个视频
    for i in range(10):
        # 点赞
        click_phone(device_id, 664, 900, "点赞")
        log_info(f"设备 {device_id}: 已点赞第 {i+1} 个视频")
        
        time.sleep(2)
        
        # 滑动到下一个视频
        touch_swip(device_id, 528, 800, 528, 410, 200)
        time.sleep(3)
    
    log_success(f"设备 {device_id} 点赞脚本执行完成")`,
      
      douyin_follow: `# 抖音自动关注脚本
import time

for device_id in device_ids:
    log_info(f"开始在设备 {device_id} 上执行关注脚本")
    
    # 打开抖音
    open_app(device_id, "com.ss.android.ugc.aweme")
    time.sleep(3)
    
    # 循环关注5个用户
    for i in range(5):
        # 点赞
        click_phone(device_id, 664, 900, "点赞")
        time.sleep(1)
        
        # 关注
        click_phone(device_id, 664, 823, "关注")
        log_info(f"设备 {device_id}: 已关注第 {i+1} 个用户")
        time.sleep(2)
        
        # 滑动到下一个视频
        touch_swip(device_id, 528, 800, 528, 410, 200)
        time.sleep(3)
    
    log_success(f"设备 {device_id} 关注脚本执行完成")`
    };

    // 方法
    const onCodeChange = () => {
      emit('update:modelValue', code.value);
      updateLineNumbers();
      validateSyntax();
      updateCursorPosition();
    };

    const onKeyDown = (event) => {
      // Tab键缩进
      if (event.key === 'Tab') {
        event.preventDefault();
        const start = event.target.selectionStart;
        const end = event.target.selectionEnd;
        code.value = code.value.substring(0, start) + '    ' + code.value.substring(end);
        nextTick(() => {
          event.target.selectionStart = event.target.selectionEnd = start + 4;
        });
      }
    };

    const updateLineNumbers = () => {
      if (!lineNumbers.value) return;
      const lines = code.value.split('\n').length;
      lineNumbers.value.innerHTML = Array.from({ length: lines }, (_, i) => i + 1).join('\n');
    };

    const updateCursorPosition = () => {
      if (!codeTextarea.value) return;
      const textarea = codeTextarea.value;
      const start = textarea.selectionStart;
      const textBeforeCursor = code.value.substring(0, start);
      const lines = textBeforeCursor.split('\n');
      currentLine.value = lines.length;
      currentColumn.value = lines[lines.length - 1].length + 1;
    };

    const validateSyntax = () => {
      // 简单的Python语法检查
      syntaxErrors.value = [];
      const lines = code.value.split('\n');
      
      lines.forEach((line, index) => {
        const lineNum = index + 1;
        const trimmed = line.trim();
        
        // 检查缩进
        if (trimmed && line.length > 0 && line[0] === ' ' && line.indexOf('    ') !== 0) {
          if (line.match(/^ +/) && !line.match(/^    /)) {
            syntaxErrors.value.push({
              line: lineNum,
              message: '缩进应该使用4个空格'
            });
          }
        }
        
        // 检查括号匹配
        const openBrackets = (line.match(/\(/g) || []).length;
        const closeBrackets = (line.match(/\)/g) || []).length;
        if (openBrackets !== closeBrackets) {
          syntaxErrors.value.push({
            line: lineNum,
            message: '括号不匹配'
          });
        }
      });
    };

    const formatCode = () => {
      // 简单的代码格式化
      const lines = code.value.split('\n');
      let indentLevel = 0;
      const formatted = lines.map(line => {
        const trimmed = line.trim();
        if (!trimmed) return '';
        
        if (trimmed.endsWith(':')) {
          const result = '    '.repeat(indentLevel) + trimmed;
          indentLevel++;
          return result;
        } else if (trimmed.startsWith('except') || trimmed.startsWith('elif') || trimmed.startsWith('else')) {
          indentLevel = Math.max(0, indentLevel - 1);
          const result = '    '.repeat(indentLevel) + trimmed;
          indentLevel++;
          return result;
        } else {
          return '    '.repeat(indentLevel) + trimmed;
        }
      });
      
      code.value = formatted.join('\n');
      ElMessage.success('代码格式化完成');
    };

    const insertFunction = (func) => {
      const textarea = codeTextarea.value;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const before = code.value.substring(0, start);
      const after = code.value.substring(end);
      
      code.value = before + func.template + after;
      
      nextTick(() => {
        textarea.focus();
        textarea.selectionStart = textarea.selectionEnd = start + func.template.length;
      });
    };

    const loadTemplate = (templateName) => {
      if (templateName && templates[templateName]) {
        code.value = templates[templateName];
        ElMessage.success('模板加载成功');
      }
    };

    const goToLine = (lineNum) => {
      const textarea = codeTextarea.value;
      const lines = code.value.split('\n');
      let charCount = 0;
      
      for (let i = 0; i < lineNum - 1; i++) {
        charCount += lines[i].length + 1; // +1 for newline
      }
      
      textarea.focus();
      textarea.selectionStart = textarea.selectionEnd = charCount;
    };

    const runScript = () => {
      if (syntaxErrors.value.length > 0) {
        ElMessage.error('请先修复语法错误');
        return;
      }
      
      running.value = true;
      emit('run', code.value);
      
      setTimeout(() => {
        running.value = false;
      }, 2000);
    };

    const showHelp = () => {
      ElMessage.info('帮助文档功能开发中...');
    };

    // 监听props变化
    watch(() => props.modelValue, (newValue) => {
      code.value = newValue;
    });

    // 生命周期
    onMounted(() => {
      updateLineNumbers();
      validateSyntax();
    });

    return {
      code,
      codeTextarea,
      lineNumbers,
      syntaxHighlights,
      editorContainer,
      activeTab,
      selectedTemplate,
      running,
      currentLine,
      currentColumn,
      syntaxErrors,
      syntaxValid,
      placeholder,
      functionCategories,
      availableVariables,
      onCodeChange,
      onKeyDown,
      formatCode,
      validateSyntax,
      insertFunction,
      loadTemplate,
      goToLine,
      runScript,
      showHelp
    };
  }
};
</script>

<style scoped>
.code-editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
  color: #d4d4d4;
  border-radius: 8px;
  overflow: hidden;
}

.editor-toolbar {
  background: #2d2d30;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #3e3e42;
}

.toolbar-left, .toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  background: #1e1e1e;
}

.line-numbers {
  background: #252526;
  color: #858585;
  padding: 12px 8px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  text-align: right;
  user-select: none;
  border-right: 1px solid #3e3e42;
  white-space: pre;
  min-width: 50px;
}

.code-textarea {
  flex: 1;
  background: transparent;
  color: #d4d4d4;
  border: none;
  outline: none;
  padding: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  white-space: pre;
  overflow-wrap: normal;
  overflow-x: auto;
}

.code-textarea::placeholder {
  color: #6a6a6a;
}

.editor-sidebar {
  width: 300px;
  background: #252526;
  border-left: 1px solid #3e3e42;
  overflow: hidden;
}

.sidebar-tabs {
  height: 100%;
}

.sidebar-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
  padding: 0;
}

.function-list, .variable-list, .error-list {
  padding: 8px;
}

.function-category {
  margin-bottom: 16px;
}

.category-title {
  color: #569cd6;
  font-size: 12px;
  font-weight: bold;
  margin: 0 0 8px 0;
  padding: 4px 8px;
  background: rgba(86, 156, 214, 0.1);
  border-radius: 4px;
}

.function-item {
  padding: 8px;
  margin: 2px 0;
  background: #2d2d30;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.function-item:hover {
  background: #3e3e42;
}

.function-name {
  color: #dcdcaa;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  margin-bottom: 2px;
}

.function-desc {
  color: #6a9955;
  font-size: 11px;
}

.variable-item {
  padding: 8px;
  margin: 2px 0;
  background: #2d2d30;
  border-radius: 4px;
}

.variable-name {
  color: #9cdcfe;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  margin-bottom: 2px;
}

.variable-type {
  color: #4ec9b0;
  font-size: 11px;
  margin-bottom: 2px;
}

.variable-desc {
  color: #6a9955;
  font-size: 11px;
}

.error-item {
  padding: 8px;
  margin: 2px 0;
  background: rgba(244, 71, 71, 0.1);
  border-left: 3px solid #f44747;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.error-item:hover {
  background: rgba(244, 71, 71, 0.2);
}

.error-icon {
  color: #f44747;
  margin-top: 2px;
}

.error-content {
  flex: 1;
}

.error-message {
  color: #f44747;
  font-size: 12px;
  margin-bottom: 2px;
}

.error-location {
  color: #858585;
  font-size: 11px;
}

.editor-status {
  background: #007acc;
  color: white;
  padding: 4px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.status-left {
  display: flex;
  gap: 16px;
}

.status-item {
  padding: 2px 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

/* 深色主题按钮样式 */
.editor-toolbar :deep(.el-button) {
  background: #3c3c3c;
  border-color: #3c3c3c;
  color: #cccccc;
}

.editor-toolbar :deep(.el-button:hover) {
  background: #464647;
  border-color: #464647;
  color: #ffffff;
}

.editor-toolbar :deep(.el-button--primary) {
  background: #0e639c;
  border-color: #0e639c;
}

.editor-toolbar :deep(.el-button--primary:hover) {
  background: #1177bb;
  border-color: #1177bb;
}

.editor-toolbar :deep(.el-select) {
  width: 120px;
}

.editor-toolbar :deep(.el-input__wrapper) {
  background: #3c3c3c;
  border-color: #3c3c3c;
}

.editor-toolbar :deep(.el-input__inner) {
  color: #cccccc;
}

/* 标签页样式 */
.sidebar-tabs :deep(.el-tabs__header) {
  background: #2d2d30;
  margin: 0;
}

.sidebar-tabs :deep(.el-tabs__nav-wrap::after) {
  background: #3e3e42;
}

.sidebar-tabs :deep(.el-tabs__item) {
  color: #cccccc;
  border-bottom: 1px solid transparent;
}

.sidebar-tabs :deep(.el-tabs__item.is-active) {
  color: #ffffff;
  border-bottom-color: #007acc;
}

.sidebar-tabs :deep(.el-tabs__active-bar) {
  background: #007acc;
}
</style>
