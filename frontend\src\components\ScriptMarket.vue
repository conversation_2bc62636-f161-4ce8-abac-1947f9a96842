<template>
  <div class="script-market">
    <div class="market-header">
      <h3 class="market-title">
        <el-icon><Shop /></el-icon>
        脚本模板市场
      </h3>
      <div class="market-controls">
        <el-input
          v-model="searchQuery"
          placeholder="搜索脚本模板..."
          class="search-input"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="selectedCategory" placeholder="分类" class="category-select">
          <el-option label="全部" value="" />
          <el-option label="抖音自动化" value="douyin" />
          <el-option label="设备控制" value="device" />
          <el-option label="数据采集" value="data" />
          <el-option label="测试工具" value="test" />
        </el-select>
        <el-button type="primary" @click="showUploadDialog">
          <el-icon><Upload /></el-icon>
          上传模板
        </el-button>
      </div>
    </div>

    <div class="market-content">
      <div class="template-grid">
        <div 
          v-for="template in filteredTemplates" 
          :key="template.id"
          class="template-card"
          @click="selectTemplate(template)"
        >
          <div class="template-header">
            <div class="template-icon">
              <el-icon>
                <component :is="getCategoryIcon(template.category)" />
              </el-icon>
            </div>
            <div class="template-actions">
              <el-button size="small" circle @click.stop="previewTemplate(template)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button size="small" circle @click.stop="downloadTemplate(template)">
                <el-icon><Download /></el-icon>
              </el-button>
            </div>
          </div>
          
          <div class="template-content">
            <h4 class="template-name">{{ template.name }}</h4>
            <p class="template-description">{{ template.description }}</p>
            
            <div class="template-meta">
              <el-tag :type="getCategoryType(template.category)" size="small">
                {{ getCategoryName(template.category) }}
              </el-tag>
              <div class="template-stats">
                <span class="stat-item">
                  <el-icon><Star /></el-icon>
                  {{ template.rating }}
                </span>
                <span class="stat-item">
                  <el-icon><Download /></el-icon>
                  {{ template.downloads }}
                </span>
              </div>
            </div>
            
            <div class="template-author">
              <el-avatar :size="20" :src="template.author.avatar">
                {{ template.author.name.charAt(0) }}
              </el-avatar>
              <span class="author-name">{{ template.author.name }}</span>
              <span class="publish-date">{{ formatDate(template.publishDate) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      :title="selectedTemplate?.name"
      width="80%"
      class="preview-dialog"
    >
      <div class="template-preview" v-if="selectedTemplate">
        <div class="preview-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="名称">{{ selectedTemplate.name }}</el-descriptions-item>
            <el-descriptions-item label="分类">{{ getCategoryName(selectedTemplate.category) }}</el-descriptions-item>
            <el-descriptions-item label="作者">{{ selectedTemplate.author.name }}</el-descriptions-item>
            <el-descriptions-item label="评分">
              <el-rate v-model="selectedTemplate.rating" disabled show-score />
            </el-descriptions-item>
            <el-descriptions-item label="下载量">{{ selectedTemplate.downloads }}</el-descriptions-item>
            <el-descriptions-item label="发布时间">{{ formatDate(selectedTemplate.publishDate) }}</el-descriptions-item>
          </el-descriptions>
          
          <div class="template-description-full">
            <h4>描述</h4>
            <p>{{ selectedTemplate.description }}</p>
          </div>
        </div>
        
        <div class="preview-code">
          <h4>代码预览</h4>
          <pre class="code-preview">{{ selectedTemplate.content }}</pre>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="previewVisible = false">取消</el-button>
        <el-button type="primary" @click="useTemplate(selectedTemplate)">
          <el-icon><Check /></el-icon>
          使用此模板
        </el-button>
      </template>
    </el-dialog>

    <!-- 上传模板对话框 -->
    <el-dialog
      v-model="uploadVisible"
      title="上传脚本模板"
      width="600px"
    >
      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef" label-width="80px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="uploadForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        
        <el-form-item label="分类" prop="category">
          <el-select v-model="uploadForm.category" placeholder="请选择分类">
            <el-option label="抖音自动化" value="douyin" />
            <el-option label="设备控制" value="device" />
            <el-option label="数据采集" value="data" />
            <el-option label="测试工具" value="test" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="uploadForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入模板描述"
          />
        </el-form-item>
        
        <el-form-item label="代码" prop="content">
          <el-input 
            v-model="uploadForm.content" 
            type="textarea" 
            :rows="10"
            placeholder="请输入脚本代码"
          />
        </el-form-item>
        
        <el-form-item label="标签">
          <el-tag
            v-for="tag in uploadForm.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model="tagInputValue"
            size="small"
            @keyup.enter="addTag"
            @blur="addTag"
            class="tag-input"
          />
          <el-button v-else size="small" @click="showTagInput">+ 添加标签</el-button>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="uploadVisible = false">取消</el-button>
        <el-button type="primary" @click="submitTemplate" :loading="uploading">
          <el-icon><Upload /></el-icon>
          上传
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Shop, Search, Upload, View, Download, Star, Check,
  VideoPlay, Monitor, DataAnalysis, Tools
} from '@element-plus/icons-vue';

export default {
  name: 'ScriptMarket',
  components: {
    Shop, Search, Upload, View, Download, Star, Check,
    VideoPlay, Monitor, DataAnalysis, Tools
  },
  emits: ['template-selected'],
  setup(props, { emit }) {
    const searchQuery = ref('');
    const selectedCategory = ref('');
    const previewVisible = ref(false);
    const uploadVisible = ref(false);
    const selectedTemplate = ref(null);
    const uploading = ref(false);
    
    // 标签输入
    const tagInputVisible = ref(false);
    const tagInputValue = ref('');
    const tagInputRef = ref(null);
    
    // 上传表单
    const uploadForm = ref({
      name: '',
      category: '',
      description: '',
      content: '',
      tags: []
    });
    
    const uploadFormRef = ref(null);
    
    const uploadRules = {
      name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
      category: [{ required: true, message: '请选择分类', trigger: 'change' }],
      description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
      content: [{ required: true, message: '请输入代码', trigger: 'blur' }]
    };

    // 模板数据
    const templates = ref([
      {
        id: 1,
        name: '抖音自动点赞脚本',
        description: '自动为抖音视频点赞并滑动到下一个视频，支持自定义点赞数量和间隔时间',
        category: 'douyin',
        rating: 4.8,
        downloads: 1250,
        author: {
          name: '开发者A',
          avatar: ''
        },
        publishDate: new Date('2024-01-15'),
        content: `# 抖音自动点赞脚本
import time
import random

for device_id in device_ids:
    log_info(f"开始在设备 {device_id} 上执行点赞脚本")
    
    # 打开抖音
    open_app(device_id, "com.ss.android.ugc.aweme")
    time.sleep(3)
    
    # 循环点赞10个视频
    for i in range(10):
        # 点赞
        click_phone(device_id, 664, 900, "点赞")
        log_info(f"设备 {device_id}: 已点赞第 {i+1} 个视频")
        
        time.sleep(2)
        
        # 滑动到下一个视频
        touch_swip(device_id, 528, 800, 528, 410, 200)
        time.sleep(3)
    
    log_success(f"设备 {device_id} 点赞脚本执行完成")`
      },
      {
        id: 2,
        name: '设备性能监控脚本',
        description: '监控设备CPU、内存、电池等性能指标，并生成报告',
        category: 'device',
        rating: 4.6,
        downloads: 890,
        author: {
          name: '开发者B',
          avatar: ''
        },
        publishDate: new Date('2024-01-20'),
        content: `# 设备性能监控脚本
import time
import json

for device_id in device_ids:
    log_info(f"开始监控设备 {device_id} 性能")
    
    # 获取设备信息
    device_info = get_device_info(device_id)
    log_info(f"设备信息: {json.dumps(device_info, indent=2)}")
    
    # 监控5分钟
    for i in range(60):  # 每5秒检查一次，共5分钟
        performance = get_device_performance(device_id)
        log_info(f"CPU: {performance['cpu']}%, 内存: {performance['memory']}%, 电池: {performance['battery']}%")
        time.sleep(5)
    
    log_success(f"设备 {device_id} 性能监控完成")`
      }
    ]);

    // 计算属性
    const filteredTemplates = computed(() => {
      return templates.value.filter(template => {
        const matchesSearch = !searchQuery.value || 
          template.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          template.description.toLowerCase().includes(searchQuery.value.toLowerCase());
        
        const matchesCategory = !selectedCategory.value || template.category === selectedCategory.value;
        
        return matchesSearch && matchesCategory;
      });
    });

    // 方法
    const getCategoryIcon = (category) => {
      const iconMap = {
        'douyin': 'VideoPlay',
        'device': 'Monitor',
        'data': 'DataAnalysis',
        'test': 'Tools'
      };
      return iconMap[category] || 'Document';
    };

    const getCategoryType = (category) => {
      const typeMap = {
        'douyin': 'success',
        'device': 'primary',
        'data': 'warning',
        'test': 'info'
      };
      return typeMap[category] || 'default';
    };

    const getCategoryName = (category) => {
      const nameMap = {
        'douyin': '抖音自动化',
        'device': '设备控制',
        'data': '数据采集',
        'test': '测试工具'
      };
      return nameMap[category] || '其他';
    };

    const formatDate = (date) => {
      return date.toLocaleDateString('zh-CN');
    };

    const selectTemplate = (template) => {
      selectedTemplate.value = template;
      previewVisible.value = true;
    };

    const previewTemplate = (template) => {
      selectedTemplate.value = template;
      previewVisible.value = true;
    };

    const downloadTemplate = async (template) => {
      try {
        // 创建下载链接
        const blob = new Blob([template.content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${template.name}.py`;
        a.click();
        URL.revokeObjectURL(url);
        
        ElMessage.success('模板下载成功');
      } catch (error) {
        ElMessage.error('下载失败: ' + error.message);
      }
    };

    const useTemplate = (template) => {
      emit('template-selected', template);
      previewVisible.value = false;
      ElMessage.success(`已选择模板: ${template.name}`);
    };

    const showUploadDialog = () => {
      uploadForm.value = {
        name: '',
        category: '',
        description: '',
        content: '',
        tags: []
      };
      uploadVisible.value = true;
    };

    const showTagInput = () => {
      tagInputVisible.value = true;
      nextTick(() => {
        tagInputRef.value?.focus();
      });
    };

    const addTag = () => {
      const tag = tagInputValue.value.trim();
      if (tag && !uploadForm.value.tags.includes(tag)) {
        uploadForm.value.tags.push(tag);
      }
      tagInputValue.value = '';
      tagInputVisible.value = false;
    };

    const removeTag = (tag) => {
      const index = uploadForm.value.tags.indexOf(tag);
      if (index > -1) {
        uploadForm.value.tags.splice(index, 1);
      }
    };

    const submitTemplate = async () => {
      try {
        await uploadFormRef.value?.validate();
        
        uploading.value = true;
        
        // 模拟上传
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 添加到模板列表
        const newTemplate = {
          id: templates.value.length + 1,
          ...uploadForm.value,
          rating: 0,
          downloads: 0,
          author: {
            name: '当前用户',
            avatar: ''
          },
          publishDate: new Date()
        };
        
        templates.value.unshift(newTemplate);
        
        uploadVisible.value = false;
        ElMessage.success('模板上传成功');
      } catch (error) {
        ElMessage.error('上传失败: ' + error.message);
      } finally {
        uploading.value = false;
      }
    };

    return {
      searchQuery,
      selectedCategory,
      previewVisible,
      uploadVisible,
      selectedTemplate,
      uploading,
      tagInputVisible,
      tagInputValue,
      tagInputRef,
      uploadForm,
      uploadFormRef,
      uploadRules,
      filteredTemplates,
      getCategoryIcon,
      getCategoryType,
      getCategoryName,
      formatDate,
      selectTemplate,
      previewTemplate,
      downloadTemplate,
      useTemplate,
      showUploadDialog,
      showTagInput,
      addTag,
      removeTag,
      submitTemplate
    };
  }
};
</script>

<style scoped>
.script-market {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.market-header {
  background: white;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.market-title {
  margin: 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
}

.market-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-input {
  width: 250px;
}

.category-select {
  width: 120px;
}

.market-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.template-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.template-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.template-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover .template-actions {
  opacity: 1;
}

.template-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-name {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
}

.template-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-stats {
  display: flex;
  gap: 12px;
  color: #909399;
  font-size: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.template-author {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #909399;
}

.author-name {
  font-weight: 500;
  color: #606266;
}

.publish-date {
  margin-left: auto;
}

/* 预览对话框样式 */
.preview-dialog {
  max-height: 80vh;
}

.template-preview {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.preview-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.template-description-full {
  margin-top: 16px;
}

.template-description-full h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.template-description-full p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.preview-code {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.preview-code h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.code-preview {
  background: #1e1e1e;
  color: #d4d4d4;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
  margin: 0;
  white-space: pre-wrap;
}

/* 上传表单样式 */
.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tag-input {
  width: 100px;
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: 1fr;
  }

  .market-header {
    flex-direction: column;
    align-items: stretch;
  }

  .market-controls {
    justify-content: space-between;
  }

  .search-input {
    width: 100%;
    max-width: 250px;
  }

  .template-actions {
    opacity: 1;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-card {
  animation: fadeInUp 0.3s ease;
}

/* 评分组件样式 */
.preview-info :deep(.el-rate) {
  display: inline-flex;
  align-items: center;
}

/* 描述组件样式 */
.preview-info :deep(.el-descriptions__body) {
  background: white;
}

.preview-info :deep(.el-descriptions__table) {
  border-radius: 6px;
  overflow: hidden;
}
</style>
