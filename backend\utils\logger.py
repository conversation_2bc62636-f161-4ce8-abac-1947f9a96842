"""
日志管理模块
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

from config import get_config

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class LogManager:
    """日志管理器"""
    
    def __init__(self):
        self.loggers = {}
        self.log_dir = Path(get_config("paths.logs_dir", "logs"))
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置根日志器
        self._setup_root_logger()
    
    def _setup_root_logger(self):
        """设置根日志器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, get_config("server.log_level", "INFO").upper()))
        
        # 彩色格式化器
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = self.log_dir / f"app_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 文件格式化器
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def create_module_logger(self, module_name: str, log_file: Optional[str] = None) -> logging.Logger:
        """为模块创建专用日志器"""
        logger = logging.getLogger(module_name)
        
        if log_file:
            # 创建模块专用的文件处理器
            log_path = self.log_dir / log_file
            handler = logging.handlers.RotatingFileHandler(
                log_path,
                maxBytes=5*1024*1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            handler.setLevel(logging.DEBUG)
            
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def cleanup_old_logs(self, days: int = 7):
        """清理旧日志文件"""
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
            
            for log_file in self.log_dir.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    logging.info(f"删除旧日志文件: {log_file}")
                    
        except Exception as e:
            logging.error(f"清理日志文件失败: {e}")

# 全局日志管理器实例
log_manager = LogManager()

# 便捷函数
def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return log_manager.get_logger(name)

def create_module_logger(module_name: str, log_file: Optional[str] = None) -> logging.Logger:
    """创建模块日志器"""
    return log_manager.create_module_logger(module_name, log_file)

# 创建各模块的专用日志器
device_logger = create_module_logger("device_manager", "device.log")
script_logger = create_module_logger("script_manager", "script.log")
scrcpy_logger = create_module_logger("scrcpy_manager", "scrcpy.log")
websocket_logger = create_module_logger("websocket_manager", "websocket.log")

# 日志装饰器
def log_function_call(logger: logging.Logger):
    """记录函数调用的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger.debug(f"调用函数 {func.__name__}, 参数: args={args}, kwargs={kwargs}")
            try:
                result = func(*args, **kwargs)
                logger.debug(f"函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行失败: {e}")
                raise
        return wrapper
    return decorator

def log_async_function_call(logger: logging.Logger):
    """记录异步函数调用的装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            logger.debug(f"调用异步函数 {func.__name__}, 参数: args={args}, kwargs={kwargs}")
            try:
                result = await func(*args, **kwargs)
                logger.debug(f"异步函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                logger.error(f"异步函数 {func.__name__} 执行失败: {e}")
                raise
        return wrapper
    return decorator

# 性能监控装饰器
def log_performance(logger: logging.Logger, threshold: float = 1.0):
    """记录函数执行时间的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                if execution_time > threshold:
                    logger.warning(f"函数 {func.__name__} 执行时间过长: {execution_time:.2f}秒")
                else:
                    logger.debug(f"函数 {func.__name__} 执行时间: {execution_time:.2f}秒")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"函数 {func.__name__} 执行失败 (耗时 {execution_time:.2f}秒): {e}")
                raise
        return wrapper
    return decorator

# 定期清理日志
import atexit
atexit.register(lambda: log_manager.cleanup_old_logs(get_config("script.log_retention_days", 7)))
