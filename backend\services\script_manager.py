"""
脚本管理服务
管理脚本的创建、执行、状态监控等功能
"""

import asyncio
import json
import logging
import os
import sys
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import importlib.util
import traceback
from concurrent.futures import ThreadPoolExecutor
import threading

from models.script import Script, ScriptExecution, ScriptLog, ScriptStatus, LogLevel

logger = logging.getLogger(__name__)

class ScriptManager:
    """脚本管理器"""
    
    def __init__(self):
        self.scripts: Dict[str, Script] = {}
        self.executions: Dict[str, ScriptExecution] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.script_logs: List[ScriptLog] = []
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.scripts_dir = "scripts"
        self.data_dir = "data"
        
        # 确保目录存在
        os.makedirs(self.scripts_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 加载现有脚本
        asyncio.create_task(self._load_scripts())
    
    async def _load_scripts(self):
        """加载现有脚本"""
        try:
            # 从文件系统加载脚本
            scripts_file = os.path.join(self.data_dir, "scripts.json")
            if os.path.exists(scripts_file):
                with open(scripts_file, 'r', encoding='utf-8') as f:
                    scripts_data = json.load(f)
                    for script_data in scripts_data:
                        script = Script(**script_data)
                        self.scripts[script.id] = script
            
            # 加载script文件夹中的Python脚本
            await self._load_python_scripts()
            
            logger.info(f"加载了 {len(self.scripts)} 个脚本")
            
        except Exception as e:
            logger.error(f"加载脚本时出错: {e}")
    
    async def _load_python_scripts(self):
        """加载Python脚本文件"""
        try:
            script_files = [f for f in os.listdir(self.scripts_dir) if f.endswith('.py')]
            
            for script_file in script_files:
                script_path = os.path.join(self.scripts_dir, script_file)
                script_name = os.path.splitext(script_file)[0]
                
                # 检查是否已存在
                existing_script = None
                for script in self.scripts.values():
                    if script.name == script_name:
                        existing_script = script
                        break
                
                if not existing_script:
                    # 读取脚本内容
                    with open(script_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 创建脚本对象
                    script = Script(
                        name=script_name,
                        content=content,
                        description=f"从文件 {script_file} 加载的脚本"
                    )
                    self.scripts[script.id] = script
                    
                    logger.info(f"加载脚本文件: {script_file}")
                    
        except Exception as e:
            logger.error(f"加载Python脚本文件时出错: {e}")
    
    async def _save_scripts(self):
        """保存脚本到文件"""
        try:
            scripts_data = [script.dict() for script in self.scripts.values()]
            scripts_file = os.path.join(self.data_dir, "scripts.json")
            
            with open(scripts_file, 'w', encoding='utf-8') as f:
                json.dump(scripts_data, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"保存脚本时出错: {e}")
    
    async def get_all_scripts(self) -> List[Script]:
        """获取所有脚本"""
        return list(self.scripts.values())
    
    async def get_script(self, script_id: str) -> Optional[Script]:
        """获取指定脚本"""
        return self.scripts.get(script_id)
    
    async def create_script(self, name: str, content: str, description: str = "") -> Script:
        """创建新脚本"""
        script = Script(
            name=name,
            content=content,
            description=description
        )
        
        self.scripts[script.id] = script
        await self._save_scripts()
        
        logger.info(f"创建脚本: {name} (ID: {script.id})")
        return script
    
    async def update_script(self, script_id: str, name: Optional[str] = None, 
                          content: Optional[str] = None, description: Optional[str] = None) -> Optional[Script]:
        """更新脚本"""
        script = self.scripts.get(script_id)
        if not script:
            return None
        
        if name is not None:
            script.name = name
        if content is not None:
            script.content = content
        if description is not None:
            script.description = description
        
        script.updated_at = datetime.now()
        await self._save_scripts()
        
        logger.info(f"更新脚本: {script.name} (ID: {script_id})")
        return script
    
    async def delete_script(self, script_id: str) -> bool:
        """删除脚本"""
        if script_id not in self.scripts:
            return False
        
        # 如果脚本正在运行，先停止
        if script_id in self.running_tasks:
            await self.stop_script(script_id)
        
        script = self.scripts[script_id]
        del self.scripts[script_id]
        await self._save_scripts()
        
        logger.info(f"删除脚本: {script.name} (ID: {script_id})")
        return True
    
    async def run_script(self, script_id: str, device_ids: List[str]) -> str:
        """运行脚本"""
        script = self.scripts.get(script_id)
        if not script:
            raise ValueError(f"脚本 {script_id} 不存在")
        
        if script_id in self.running_tasks:
            raise ValueError(f"脚本 {script.name} 已在运行中")
        
        # 创建执行记录
        execution = ScriptExecution(
            script_id=script_id,
            device_ids=device_ids
        )
        self.executions[execution.id] = execution
        
        # 更新脚本状态
        script.status = ScriptStatus.RUNNING
        script.last_run_at = datetime.now()
        script.run_count += 1
        
        # 创建执行任务
        task = asyncio.create_task(self._execute_script(script, execution))
        self.running_tasks[script_id] = task
        
        logger.info(f"开始执行脚本: {script.name} (ID: {script_id})")
        return execution.id
    
    async def _execute_script(self, script: Script, execution: ScriptExecution):
        """执行脚本的内部方法"""
        start_time = datetime.now()
        
        try:
            # 记录开始日志
            await self._add_log(execution, LogLevel.INFO, f"开始执行脚本: {script.name}")
            
            # 准备执行环境
            script_globals = self._prepare_script_environment(script, execution)
            
            # 执行脚本
            await self._run_script_code(script, execution, script_globals)
            
            # 更新执行状态
            execution.status = ScriptStatus.COMPLETED
            execution.finished_at = datetime.now()
            execution.runtime = (execution.finished_at - start_time).total_seconds()
            
            # 更新脚本统计
            script.status = ScriptStatus.STOPPED
            script.success_count += 1
            script.total_runtime += execution.runtime
            script.average_runtime = script.total_runtime / script.run_count
            
            await self._add_log(execution, LogLevel.SUCCESS, f"脚本执行完成，耗时: {execution.runtime:.2f}秒")
            
        except Exception as e:
            # 处理执行错误
            execution.status = ScriptStatus.ERROR
            execution.finished_at = datetime.now()
            execution.runtime = (execution.finished_at - start_time).total_seconds()
            execution.error_message = str(e)
            
            script.status = ScriptStatus.ERROR
            script.error_count += 1
            
            await self._add_log(execution, LogLevel.ERROR, f"脚本执行失败: {str(e)}")
            logger.error(f"脚本执行失败: {e}\n{traceback.format_exc()}")
            
        finally:
            # 清理任务
            if script.id in self.running_tasks:
                del self.running_tasks[script.id]
    
    def _prepare_script_environment(self, script: Script, execution: ScriptExecution) -> Dict[str, Any]:
        """准备脚本执行环境"""
        # 导入必要的模块
        script_globals = {
            '__builtins__': __builtins__,
            'asyncio': asyncio,
            'time': __import__('time'),
            'random': __import__('random'),
            'json': json,
            'os': os,
            'sys': sys,
            'datetime': __import__('datetime'),
            'logging': logging,
            
            # 脚本执行上下文
            'script_id': script.id,
            'script_name': script.name,
            'execution_id': execution.id,
            'device_ids': execution.device_ids,
            
            # 日志函数
            'log_info': lambda msg: asyncio.create_task(self._add_log(execution, LogLevel.INFO, msg)),
            'log_success': lambda msg: asyncio.create_task(self._add_log(execution, LogLevel.SUCCESS, msg)),
            'log_warning': lambda msg: asyncio.create_task(self._add_log(execution, LogLevel.WARNING, msg)),
            'log_error': lambda msg: asyncio.create_task(self._add_log(execution, LogLevel.ERROR, msg)),
            
            # 设备控制函数 (需要从device_manager获取)
            'click_phone': self._create_device_function('click_phone'),
            'input_text': self._create_device_function('input_text'),
            'touch_swip': self._create_device_function('touch_swip'),
            'open_app': self._create_device_function('open_app'),
        }
        
        return script_globals
    
    def _create_device_function(self, function_name: str):
        """创建设备控制函数的包装器"""
        def wrapper(*args, **kwargs):
            # 这里需要调用device_manager的相应方法
            # 暂时返回模拟结果
            return f"执行 {function_name} 参数: {args}, {kwargs}"
        return wrapper

    async def _run_script_code(self, script: Script, execution: ScriptExecution, script_globals: Dict[str, Any]):
        """运行脚本代码"""
        try:
            # 编译脚本代码
            compiled_code = compile(script.content, f"<script:{script.name}>", "exec")

            # 在线程池中执行脚本 (避免阻塞事件循环)
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor,
                exec,
                compiled_code,
                script_globals
            )

        except SyntaxError as e:
            raise Exception(f"脚本语法错误: {e}")
        except Exception as e:
            raise Exception(f"脚本执行错误: {e}")

    async def _add_log(self, execution: ScriptExecution, level: LogLevel, message: str,
                      device_id: Optional[str] = None):
        """添加执行日志"""
        log = ScriptLog(
            execution_id=execution.id,
            script_id=execution.script_id,
            device_id=device_id,
            level=level,
            message=message
        )

        execution.logs.append(log)
        self.script_logs.append(log)

        # 保持日志数量在合理范围内
        if len(self.script_logs) > 10000:
            self.script_logs = self.script_logs[-5000:]

        logger.info(f"[{level.value.upper()}] {message}")

    async def stop_script(self, script_id: str) -> bool:
        """停止脚本执行"""
        if script_id not in self.running_tasks:
            return False

        script = self.scripts.get(script_id)
        if not script:
            return False

        try:
            # 取消任务
            task = self.running_tasks[script_id]
            task.cancel()

            try:
                await task
            except asyncio.CancelledError:
                pass

            # 更新状态
            script.status = ScriptStatus.STOPPED

            # 更新执行记录
            for execution in self.executions.values():
                if execution.script_id == script_id and execution.status == ScriptStatus.RUNNING:
                    execution.status = ScriptStatus.STOPPED
                    execution.finished_at = datetime.now()
                    if execution.started_at:
                        execution.runtime = (execution.finished_at - execution.started_at).total_seconds()

            logger.info(f"停止脚本: {script.name} (ID: {script_id})")
            return True

        except Exception as e:
            logger.error(f"停止脚本时出错: {e}")
            return False

    async def stop_all_scripts(self):
        """停止所有运行中的脚本"""
        logger.info("停止所有运行中的脚本...")

        tasks = []
        for script_id in list(self.running_tasks.keys()):
            tasks.append(self.stop_script(script_id))

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        logger.info("所有脚本已停止")

    async def get_script_status(self, script_id: str) -> Optional[Dict]:
        """获取脚本状态"""
        script = self.scripts.get(script_id)
        if not script:
            return None

        # 查找最近的执行记录
        latest_execution = None
        for execution in self.executions.values():
            if execution.script_id == script_id:
                if not latest_execution or execution.started_at > latest_execution.started_at:
                    latest_execution = execution

        status = {
            'script_id': script_id,
            'name': script.name,
            'status': script.status.value,
            'run_count': script.run_count,
            'success_count': script.success_count,
            'error_count': script.error_count,
            'average_runtime': script.average_runtime,
            'last_run_at': script.last_run_at.isoformat() if script.last_run_at else None,
            'is_running': script_id in self.running_tasks
        }

        if latest_execution:
            status['latest_execution'] = {
                'id': latest_execution.id,
                'status': latest_execution.status.value,
                'started_at': latest_execution.started_at.isoformat(),
                'finished_at': latest_execution.finished_at.isoformat() if latest_execution.finished_at else None,
                'runtime': latest_execution.runtime,
                'device_ids': latest_execution.device_ids,
                'error_message': latest_execution.error_message
            }

        return status

    async def get_execution_logs(self, execution_id: str) -> List[ScriptLog]:
        """获取执行日志"""
        execution = self.executions.get(execution_id)
        if not execution:
            return []

        return execution.logs

    async def get_recent_logs(self, limit: int = 100) -> List[ScriptLog]:
        """获取最近的日志"""
        return self.script_logs[-limit:] if len(self.script_logs) > limit else self.script_logs

    async def clear_logs(self):
        """清空日志"""
        self.script_logs.clear()
        for execution in self.executions.values():
            execution.logs.clear()

        logger.info("已清空所有脚本日志")

    async def get_script_statistics(self) -> Dict:
        """获取脚本统计信息"""
        total_scripts = len(self.scripts)
        running_scripts = len(self.running_tasks)
        total_executions = len(self.executions)

        total_runtime = sum(script.total_runtime for script in self.scripts.values())
        total_runs = sum(script.run_count for script in self.scripts.values())
        total_successes = sum(script.success_count for script in self.scripts.values())
        total_errors = sum(script.error_count for script in self.scripts.values())

        return {
            'total_scripts': total_scripts,
            'running_scripts': running_scripts,
            'total_executions': total_executions,
            'total_runtime': total_runtime,
            'total_runs': total_runs,
            'total_successes': total_successes,
            'total_errors': total_errors,
            'success_rate': (total_successes / total_runs * 100) if total_runs > 0 else 0,
            'average_runtime': total_runtime / total_runs if total_runs > 0 else 0
        }

    def set_device_manager(self, device_manager):
        """设置设备管理器引用"""
        self.device_manager = device_manager

        # 重新创建设备控制函数
        def create_device_function(function_name: str):
            async def wrapper(*args, **kwargs):
                if hasattr(self.device_manager, function_name):
                    return await getattr(self.device_manager, function_name)(*args, **kwargs)
                else:
                    return f"设备管理器中不存在方法: {function_name}"
            return wrapper

        self._create_device_function = create_device_function
