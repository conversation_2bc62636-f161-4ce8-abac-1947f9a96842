"""
设备数据模型
"""

from pydantic import BaseModel
from enum import Enum
from typing import Optional, Dict, Any
from datetime import datetime

class DeviceStatus(str, Enum):
    """设备状态枚举"""
    ONLINE = "online"
    OFFLINE = "offline"
    CONNECTING = "connecting"
    DISCONNECTING = "disconnecting"
    ERROR = "error"

class DeviceType(str, Enum):
    """设备类型枚举"""
    ANDROID = "android"
    IOS = "ios"
    UNKNOWN = "unknown"

class Device(BaseModel):
    """设备模型"""
    id: str  # 设备ID (ADB设备序列号)
    name: Optional[str] = None  # 设备名称
    status: DeviceStatus = DeviceStatus.OFFLINE  # 设备状态
    device_type: DeviceType = DeviceType.ANDROID  # 设备类型
    model: Optional[str] = None  # 设备型号
    android_version: Optional[str] = None  # Android版本
    resolution: Optional[str] = None  # 屏幕分辨率
    density: Optional[int] = None  # 屏幕密度
    battery_level: Optional[int] = None  # 电池电量
    cpu_usage: Optional[float] = None  # CPU使用率
    memory_usage: Optional[float] = None  # 内存使用率
    temperature: Optional[float] = None  # 设备温度
    ip_address: Optional[str] = None  # IP地址
    connected_at: Optional[datetime] = None  # 连接时间
    last_seen: Optional[datetime] = None  # 最后活跃时间
    scrcpy_running: bool = False  # scrcpy是否运行中
    scrcpy_port: Optional[int] = None  # scrcpy端口
    properties: Dict[str, Any] = {}  # 其他属性
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class DeviceCommand(BaseModel):
    """设备命令模型"""
    device_id: str
    command: str
    parameters: Dict[str, Any] = {}
    timeout: int = 30

class DeviceResponse(BaseModel):
    """设备响应模型"""
    device_id: str
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: datetime = datetime.now()
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class TouchEvent(BaseModel):
    """触摸事件模型"""
    device_id: str
    x: int
    y: int
    action: str = "tap"  # tap, swipe, long_press
    duration: Optional[int] = None  # 持续时间(毫秒)
    end_x: Optional[int] = None  # 滑动结束X坐标
    end_y: Optional[int] = None  # 滑动结束Y坐标

class KeyEvent(BaseModel):
    """按键事件模型"""
    device_id: str
    key_code: str  # 按键代码或特殊键名
    action: str = "press"  # press, down, up

class TextInput(BaseModel):
    """文本输入模型"""
    device_id: str
    text: str
    clear_before: bool = False  # 输入前是否清空

class AppOperation(BaseModel):
    """应用操作模型"""
    device_id: str
    package_name: str
    action: str  # start, stop, install, uninstall, clear_data
    apk_path: Optional[str] = None  # APK文件路径(用于安装)
