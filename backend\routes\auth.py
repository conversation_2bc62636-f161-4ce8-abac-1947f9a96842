"""
认证相关API路由
"""

from fastapi import APIRouter, HTTPException, Depends, Request, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from utils.security import security_manager, get_current_user, User, require_permission
from utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/auth", tags=["认证"])

class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    user: dict

class UserCreateRequest(BaseModel):
    username: str
    email: str
    password: str
    role: str = "user"
    permissions: List[str] = []

class UserUpdateRequest(BaseModel):
    email: Optional[str] = None
    role: Optional[str] = None
    permissions: Optional[List[str]] = None
    is_active: Optional[bool] = None

class PasswordChangeRequest(BaseModel):
    old_password: str
    new_password: str

class ApiKeyCreateRequest(BaseModel):
    name: str
    permissions: List[str]

@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest, req: Request):
    """用户登录"""
    try:
        # 获取客户端IP
        client_ip = req.client.host if req.client else "unknown"
        
        # 认证用户
        user = security_manager.authenticate_user(
            request.username, 
            request.password, 
            client_ip
        )
        
        # 创建JWT令牌
        access_token = security_manager.create_jwt_token(user.id)
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            user={
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "role": user.role,
                "permissions": user.permissions
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败"
        )

@router.get("/me")
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "role": current_user.role,
        "permissions": current_user.permissions,
        "is_active": current_user.is_active,
        "created_at": current_user.created_at,
        "last_login": current_user.last_login
    }

@router.post("/logout")
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出"""
    # 在实际实现中，可以将令牌加入黑名单
    logger.info(f"用户 {current_user.username} 登出")
    return {"message": "登出成功"}

@router.get("/users")
@require_permission("user:read")
async def get_users(current_user: User = Depends(get_current_user)):
    """获取用户列表"""
    users = []
    for user_data in security_manager.users.values():
        users.append({
            "id": user_data["id"],
            "username": user_data["username"],
            "email": user_data["email"],
            "role": user_data["role"],
            "permissions": user_data["permissions"],
            "is_active": user_data["is_active"],
            "created_at": user_data["created_at"],
            "last_login": user_data.get("last_login")
        })
    return users

@router.post("/users")
@require_permission("user:create")
async def create_user(
    request: UserCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建用户"""
    try:
        # 检查用户名是否已存在
        for user_data in security_manager.users.values():
            if user_data["username"] == request.username:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户名已存在"
                )
        
        # 创建新用户
        user_id = f"user_{len(security_manager.users) + 1}"
        new_user = {
            "id": user_id,
            "username": request.username,
            "email": request.email,
            "password_hash": security_manager.hash_password(request.password),
            "role": request.role,
            "permissions": request.permissions,
            "is_active": True,
            "created_at": datetime.now(),
            "last_login": None
        }
        
        security_manager.users[user_id] = new_user
        
        logger.info(f"用户 {current_user.username} 创建了新用户 {request.username}")
        
        return {
            "id": user_id,
            "username": request.username,
            "email": request.email,
            "role": request.role,
            "permissions": request.permissions,
            "is_active": True,
            "created_at": new_user["created_at"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建用户失败"
        )

@router.put("/users/{user_id}")
@require_permission("user:update")
async def update_user(
    user_id: str,
    request: UserUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新用户"""
    try:
        if user_id not in security_manager.users:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        user_data = security_manager.users[user_id]
        
        # 更新字段
        if request.email is not None:
            user_data["email"] = request.email
        if request.role is not None:
            user_data["role"] = request.role
        if request.permissions is not None:
            user_data["permissions"] = request.permissions
        if request.is_active is not None:
            user_data["is_active"] = request.is_active
        
        logger.info(f"用户 {current_user.username} 更新了用户 {user_data['username']}")
        
        return {
            "id": user_data["id"],
            "username": user_data["username"],
            "email": user_data["email"],
            "role": user_data["role"],
            "permissions": user_data["permissions"],
            "is_active": user_data["is_active"],
            "created_at": user_data["created_at"],
            "last_login": user_data.get("last_login")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户失败"
        )

@router.delete("/users/{user_id}")
@require_permission("user:delete")
async def delete_user(
    user_id: str,
    current_user: User = Depends(get_current_user)
):
    """删除用户"""
    try:
        if user_id not in security_manager.users:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 不能删除自己
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除自己"
            )
        
        user_data = security_manager.users[user_id]
        del security_manager.users[user_id]
        
        logger.info(f"用户 {current_user.username} 删除了用户 {user_data['username']}")
        
        return {"message": "用户删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )

@router.post("/change-password")
async def change_password(
    request: PasswordChangeRequest,
    current_user: User = Depends(get_current_user)
):
    """修改密码"""
    try:
        user_data = security_manager.users[current_user.id]
        
        # 验证旧密码
        if not security_manager.verify_password(request.old_password, user_data["password_hash"]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="旧密码错误"
            )
        
        # 更新密码
        user_data["password_hash"] = security_manager.hash_password(request.new_password)
        
        logger.info(f"用户 {current_user.username} 修改了密码")
        
        return {"message": "密码修改成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改密码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="修改密码失败"
        )

@router.post("/api-keys")
async def create_api_key(
    request: ApiKeyCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建API密钥"""
    try:
        api_key = security_manager.create_api_key(
            current_user.id,
            request.name,
            request.permissions
        )
        
        return {
            "api_key": api_key,
            "name": request.name,
            "permissions": request.permissions,
            "created_at": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"创建API密钥失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建API密钥失败"
        )

@router.get("/api-keys")
async def get_api_keys(current_user: User = Depends(get_current_user)):
    """获取API密钥列表"""
    keys = []
    for api_key, key_data in security_manager.api_keys.items():
        if key_data["user_id"] == current_user.id:
            keys.append({
                "name": key_data["name"],
                "permissions": key_data["permissions"],
                "created_at": key_data["created_at"],
                "last_used": key_data["last_used"],
                "is_active": key_data["is_active"],
                "key_preview": api_key[:8] + "..." + api_key[-4:]  # 只显示部分密钥
            })
    return keys

@router.get("/login-attempts")
@require_permission("security:read")
async def get_login_attempts(current_user: User = Depends(get_current_user)):
    """获取登录尝试记录"""
    attempts = []
    for attempt in security_manager.login_attempts[-100:]:  # 最近100条记录
        attempts.append({
            "ip_address": attempt.ip_address,
            "username": attempt.username,
            "timestamp": attempt.timestamp,
            "success": attempt.success
        })
    return attempts
