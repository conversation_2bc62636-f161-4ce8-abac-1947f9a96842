"""
安全管理模块
"""

import hashlib
import secrets
import jwt
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from functools import wraps
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

from config import get_config
from utils.logger import get_logger

logger = get_logger(__name__)

# JWT配置
JWT_SECRET_KEY = get_config("security.jwt_secret", secrets.token_urlsafe(32))
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = get_config("security.jwt_expiration_hours", 24)

# 安全配置
MAX_LOGIN_ATTEMPTS = get_config("security.max_login_attempts", 5)
LOCKOUT_DURATION = get_config("security.lockout_duration", 300)  # 5分钟

security = HTTPBearer()

class User(BaseModel):
    """用户模型"""
    id: str
    username: str
    email: str
    role: str
    permissions: List[str]
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None

class LoginAttempt(BaseModel):
    """登录尝试记录"""
    ip_address: str
    username: str
    timestamp: datetime
    success: bool

class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.users: Dict[str, Dict] = {}
        self.login_attempts: List[LoginAttempt] = []
        self.active_sessions: Dict[str, Dict] = {}
        self.api_keys: Dict[str, Dict] = {}
        self._init_default_users()
    
    def _init_default_users(self):
        """初始化默认用户"""
        admin_user = {
            "id": "admin",
            "username": "admin",
            "email": "<EMAIL>",
            "password_hash": self.hash_password("admin123"),
            "role": "admin",
            "permissions": ["*"],  # 所有权限
            "is_active": True,
            "created_at": datetime.now(),
            "last_login": None
        }
        
        self.users["admin"] = admin_user
        logger.info("默认管理员用户已创建")
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000
        )
        return f"{salt}:{password_hash.hex()}"
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, hash_value = password_hash.split(':')
            password_hash_check = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt.encode('utf-8'),
                100000
            )
            return password_hash_check.hex() == hash_value
        except Exception:
            return False
    
    def create_jwt_token(self, user_id: str) -> str:
        """创建JWT令牌"""
        payload = {
            "user_id": user_id,
            "exp": datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS),
            "iat": datetime.utcnow()
        }
        return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    def verify_jwt_token(self, token: str) -> Optional[Dict]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("JWT令牌已过期")
            return None
        except jwt.InvalidTokenError:
            logger.warning("无效的JWT令牌")
            return None
    
    def authenticate_user(self, username: str, password: str, ip_address: str) -> Optional[User]:
        """用户认证"""
        # 检查是否被锁定
        if self.is_ip_locked(ip_address):
            logger.warning(f"IP {ip_address} 被锁定，拒绝登录尝试")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="IP地址被锁定，请稍后再试"
            )
        
        # 查找用户
        user_data = None
        for user in self.users.values():
            if user["username"] == username:
                user_data = user
                break
        
        if not user_data:
            self.record_login_attempt(ip_address, username, False)
            logger.warning(f"用户 {username} 不存在")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 验证密码
        if not self.verify_password(password, user_data["password_hash"]):
            self.record_login_attempt(ip_address, username, False)
            logger.warning(f"用户 {username} 密码错误")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 检查用户是否激活
        if not user_data["is_active"]:
            logger.warning(f"用户 {username} 已被禁用")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户已被禁用"
            )
        
        # 记录成功登录
        self.record_login_attempt(ip_address, username, True)
        user_data["last_login"] = datetime.now()
        
        # 创建用户对象
        user = User(
            id=user_data["id"],
            username=user_data["username"],
            email=user_data["email"],
            role=user_data["role"],
            permissions=user_data["permissions"],
            is_active=user_data["is_active"],
            created_at=user_data["created_at"],
            last_login=user_data["last_login"]
        )
        
        logger.info(f"用户 {username} 登录成功")
        return user
    
    def record_login_attempt(self, ip_address: str, username: str, success: bool):
        """记录登录尝试"""
        attempt = LoginAttempt(
            ip_address=ip_address,
            username=username,
            timestamp=datetime.now(),
            success=success
        )
        self.login_attempts.append(attempt)
        
        # 清理旧记录（保留最近1小时的记录）
        cutoff_time = datetime.now() - timedelta(hours=1)
        self.login_attempts = [
            attempt for attempt in self.login_attempts
            if attempt.timestamp > cutoff_time
        ]
    
    def is_ip_locked(self, ip_address: str) -> bool:
        """检查IP是否被锁定"""
        cutoff_time = datetime.now() - timedelta(seconds=LOCKOUT_DURATION)
        
        # 获取最近的失败尝试
        recent_failures = [
            attempt for attempt in self.login_attempts
            if (attempt.ip_address == ip_address and 
                not attempt.success and 
                attempt.timestamp > cutoff_time)
        ]
        
        return len(recent_failures) >= MAX_LOGIN_ATTEMPTS
    
    def check_permission(self, user: User, permission: str) -> bool:
        """检查用户权限"""
        if "*" in user.permissions:  # 超级管理员
            return True
        
        return permission in user.permissions
    
    def create_api_key(self, user_id: str, name: str, permissions: List[str]) -> str:
        """创建API密钥"""
        api_key = secrets.token_urlsafe(32)
        self.api_keys[api_key] = {
            "user_id": user_id,
            "name": name,
            "permissions": permissions,
            "created_at": datetime.now(),
            "last_used": None,
            "is_active": True
        }
        logger.info(f"为用户 {user_id} 创建API密钥: {name}")
        return api_key
    
    def verify_api_key(self, api_key: str) -> Optional[Dict]:
        """验证API密钥"""
        key_data = self.api_keys.get(api_key)
        if not key_data or not key_data["is_active"]:
            return None
        
        key_data["last_used"] = datetime.now()
        return key_data

# 全局安全管理器实例
security_manager = SecurityManager()

# 依赖注入函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """获取当前用户"""
    token = credentials.credentials
    
    # 验证JWT令牌
    payload = security_manager.verify_jwt_token(token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_id = payload.get("user_id")
    user_data = security_manager.users.get(user_id)
    
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return User(
        id=user_data["id"],
        username=user_data["username"],
        email=user_data["email"],
        role=user_data["role"],
        permissions=user_data["permissions"],
        is_active=user_data["is_active"],
        created_at=user_data["created_at"],
        last_login=user_data.get("last_login")
    )

def require_permission(permission: str):
    """权限装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要认证"
                )
            
            if not security_manager.check_permission(current_user, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def rate_limit(max_requests: int, window_seconds: int):
    """频率限制装饰器"""
    request_counts = {}
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取客户端IP（简化实现）
            client_ip = "127.0.0.1"  # 实际应该从请求中获取
            
            current_time = time.time()
            window_start = current_time - window_seconds
            
            # 清理过期记录
            if client_ip in request_counts:
                request_counts[client_ip] = [
                    timestamp for timestamp in request_counts[client_ip]
                    if timestamp > window_start
                ]
            else:
                request_counts[client_ip] = []
            
            # 检查请求数量
            if len(request_counts[client_ip]) >= max_requests:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="请求频率过高"
                )
            
            # 记录当前请求
            request_counts[client_ip].append(current_time)
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
