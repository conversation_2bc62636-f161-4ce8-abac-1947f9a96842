"""
WebSocket连接管理器
管理WebSocket连接，实现消息广播等功能
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Set
from fastapi import WebSocket
from datetime import datetime

logger = logging.getLogger(__name__)

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_info: Dict[WebSocket, Dict] = {}
        
    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        # 记录连接信息
        self.connection_info[websocket] = {
            'connected_at': datetime.now(),
            'client_ip': websocket.client.host if websocket.client else 'unknown',
            'last_ping': datetime.now()
        }
        
        logger.info(f"WebSocket连接建立: {websocket.client.host if websocket.client else 'unknown'}")
        
        # 发送欢迎消息
        await self.send_personal_message({
            "type": "welcome",
            "data": {
                "message": "连接成功",
                "server_time": datetime.now().isoformat()
            }
        }, websocket)
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        if websocket in self.connection_info:
            info = self.connection_info[websocket]
            logger.info(f"WebSocket连接断开: {info.get('client_ip', 'unknown')}")
            del self.connection_info[websocket]
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(json.dumps(message, default=str))
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any]):
        """广播消息给所有连接的客户端"""
        if not self.active_connections:
            return
        
        message_text = json.dumps(message, default=str)
        disconnected_connections = []
        
        for connection in self.active_connections:
            try:
                await connection.send_text(message_text)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected_connections.append(connection)
        
        # 清理断开的连接
        for connection in disconnected_connections:
            self.disconnect(connection)
    
    async def broadcast_to_subset(self, message: Dict[str, Any], connections: List[WebSocket]):
        """向指定的连接子集广播消息"""
        message_text = json.dumps(message, default=str)
        disconnected_connections = []
        
        for connection in connections:
            if connection in self.active_connections:
                try:
                    await connection.send_text(message_text)
                except Exception as e:
                    logger.error(f"广播消息失败: {e}")
                    disconnected_connections.append(connection)
        
        # 清理断开的连接
        for connection in disconnected_connections:
            self.disconnect(connection)
    
    def get_connection_count(self) -> int:
        """获取当前连接数"""
        return len(self.active_connections)
    
    def get_connection_info(self) -> List[Dict]:
        """获取所有连接信息"""
        info_list = []
        for websocket, info in self.connection_info.items():
            info_list.append({
                'client_ip': info.get('client_ip', 'unknown'),
                'connected_at': info.get('connected_at').isoformat() if info.get('connected_at') else None,
                'last_ping': info.get('last_ping').isoformat() if info.get('last_ping') else None,
                'is_active': websocket in self.active_connections
            })
        return info_list
    
    async def ping_all(self):
        """向所有连接发送ping消息"""
        await self.broadcast({
            "type": "ping",
            "data": {
                "timestamp": datetime.now().isoformat()
            }
        })
    
    async def send_device_update(self, device_data: Dict[str, Any]):
        """发送设备状态更新"""
        await self.broadcast({
            "type": "device_update",
            "data": device_data
        })
    
    async def send_script_log(self, log_data: Dict[str, Any]):
        """发送脚本日志"""
        await self.broadcast({
            "type": "script_log",
            "data": log_data
        })
    
    async def send_script_status(self, script_data: Dict[str, Any]):
        """发送脚本状态更新"""
        await self.broadcast({
            "type": "script_status",
            "data": script_data
        })
    
    async def send_system_notification(self, notification: Dict[str, Any]):
        """发送系统通知"""
        await self.broadcast({
            "type": "system_notification",
            "data": notification
        })
    
    async def send_scrcpy_frame(self, device_id: str, frame_data: bytes):
        """发送scrcpy画面帧数据"""
        # 这里可以实现画面数据的传输
        # 由于WebSocket传输二进制数据的限制，可能需要使用base64编码
        import base64
        
        frame_base64 = base64.b64encode(frame_data).decode('utf-8')
        
        await self.broadcast({
            "type": "scrcpy_frame",
            "data": {
                "device_id": device_id,
                "frame": frame_base64,
                "timestamp": datetime.now().isoformat()
            }
        })
    
    async def handle_client_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """处理客户端消息"""
        message_type = message.get("type")
        data = message.get("data", {})
        
        if message_type == "pong":
            # 更新最后ping时间
            if websocket in self.connection_info:
                self.connection_info[websocket]['last_ping'] = datetime.now()
        
        elif message_type == "subscribe":
            # 处理订阅请求
            subscription_type = data.get("subscription_type")
            await self._handle_subscription(websocket, subscription_type, data)
        
        elif message_type == "unsubscribe":
            # 处理取消订阅请求
            subscription_type = data.get("subscription_type")
            await self._handle_unsubscription(websocket, subscription_type, data)
    
    async def _handle_subscription(self, websocket: WebSocket, subscription_type: str, data: Dict[str, Any]):
        """处理订阅请求"""
        # 这里可以实现更复杂的订阅逻辑
        # 例如只向订阅了特定设备的客户端发送该设备的更新
        pass
    
    async def _handle_unsubscription(self, websocket: WebSocket, subscription_type: str, data: Dict[str, Any]):
        """处理取消订阅请求"""
        # 这里可以实现取消订阅的逻辑
        pass
    
    async def cleanup_stale_connections(self):
        """清理过期连接"""
        current_time = datetime.now()
        stale_connections = []
        
        for websocket, info in self.connection_info.items():
            last_ping = info.get('last_ping')
            if last_ping and (current_time - last_ping).total_seconds() > 300:  # 5分钟无响应
                stale_connections.append(websocket)
        
        for connection in stale_connections:
            logger.info(f"清理过期连接: {self.connection_info[connection].get('client_ip', 'unknown')}")
            self.disconnect(connection)
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取WebSocket统计信息"""
        current_time = datetime.now()
        
        # 计算连接时长统计
        connection_durations = []
        for info in self.connection_info.values():
            connected_at = info.get('connected_at')
            if connected_at:
                duration = (current_time - connected_at).total_seconds()
                connection_durations.append(duration)
        
        avg_duration = sum(connection_durations) / len(connection_durations) if connection_durations else 0
        
        return {
            'total_connections': len(self.active_connections),
            'average_connection_duration': avg_duration,
            'longest_connection_duration': max(connection_durations) if connection_durations else 0,
            'connections_info': self.get_connection_info()
        }
