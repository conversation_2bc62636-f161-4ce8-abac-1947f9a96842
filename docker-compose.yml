version: '3.8'

services:
  backend:
    build: ./backend
    container_name: device-control-backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend/data:/app/data
      - ./backend/logs:/app/logs
      - ./backend/scripts:/app/scripts
      - /dev/bus/usb:/dev/bus/usb  # USB设备访问
    devices:
      - /dev/bus/usb  # USB设备访问
    privileged: true  # 需要访问USB设备
    environment:
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8000
      - LOG_LEVEL=info
    networks:
      - device-control-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build: ./frontend
    container_name: device-control-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - device-control-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: device-control-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - device-control-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL数据库（可选，用于生产环境）
  postgres:
    image: postgres:15-alpine
    container_name: device-control-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=device_control
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=password123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql:/docker-entrypoint-initdb.d
    networks:
      - device-control-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d device_control"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx负载均衡器（可选，用于多实例部署）
  nginx:
    image: nginx:alpine
    container_name: device-control-nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - device-control-network
    restart: unless-stopped

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: device-control-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - device-control-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: device-control-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - device-control-network
    restart: unless-stopped

networks:
  device-control-network:
    driver: bridge

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:
