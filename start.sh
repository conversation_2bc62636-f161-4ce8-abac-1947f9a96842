#!/bin/bash

echo "================================================"
echo "           设备群控系统启动脚本"
echo "================================================"
echo

echo "检查环境..."

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装或未添加到PATH"
    exit 1
fi
echo "✅ Python3已安装"

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装或未添加到PATH"
    exit 1
fi
echo "✅ Node.js已安装"

# 检查ADB
if ! command -v adb &> /dev/null; then
    echo "⚠️  ADB未安装或未添加到PATH，设备管理功能将无法使用"
else
    echo "✅ ADB已安装"
fi

# 检查scrcpy
if ! command -v scrcpy &> /dev/null; then
    echo "⚠️  scrcpy未安装或未添加到PATH，设备画面显示功能将无法使用"
else
    echo "✅ scrcpy已安装"
fi

echo
echo "启动服务..."
echo

# 启动后端服务
echo "启动后端服务..."
cd backend
python3 start.py &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 3

# 启动前端服务
echo "启动前端服务..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo
echo "================================================"
echo "服务启动完成！"
echo
echo "后端服务: http://localhost:8000"
echo "前端界面: http://localhost:5173"
echo "API文档: http://localhost:8000/docs"
echo
echo "按Ctrl+C停止所有服务"
echo "================================================"

# 等待用户中断
trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT

# 保持脚本运行
wait
