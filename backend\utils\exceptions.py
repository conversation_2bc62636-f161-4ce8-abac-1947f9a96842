"""
自定义异常类
"""

class DeviceControlError(Exception):
    """设备控制异常"""
    def __init__(self, device_id: str, message: str):
        self.device_id = device_id
        self.message = message
        super().__init__(f"设备 {device_id}: {message}")

class DeviceNotFoundError(DeviceControlError):
    """设备未找到异常"""
    def __init__(self, device_id: str):
        super().__init__(device_id, "设备未找到")

class DeviceOfflineError(DeviceControlError):
    """设备离线异常"""
    def __init__(self, device_id: str):
        super().__init__(device_id, "设备离线")

class DeviceConnectionError(DeviceControlError):
    """设备连接异常"""
    def __init__(self, device_id: str, message: str = "连接失败"):
        super().__init__(device_id, message)

class ScriptError(Exception):
    """脚本异常"""
    def __init__(self, script_id: str, message: str):
        self.script_id = script_id
        self.message = message
        super().__init__(f"脚本 {script_id}: {message}")

class ScriptNotFoundError(ScriptError):
    """脚本未找到异常"""
    def __init__(self, script_id: str):
        super().__init__(script_id, "脚本未找到")

class ScriptExecutionError(ScriptError):
    """脚本执行异常"""
    def __init__(self, script_id: str, message: str):
        super().__init__(script_id, f"执行失败: {message}")

class ScriptSyntaxError(ScriptError):
    """脚本语法异常"""
    def __init__(self, script_id: str, message: str):
        super().__init__(script_id, f"语法错误: {message}")

class ScrcpyError(Exception):
    """scrcpy异常"""
    def __init__(self, device_id: str, message: str):
        self.device_id = device_id
        self.message = message
        super().__init__(f"scrcpy {device_id}: {message}")

class ScrcpyStartError(ScrcpyError):
    """scrcpy启动异常"""
    def __init__(self, device_id: str, message: str = "启动失败"):
        super().__init__(device_id, message)

class ScrcpyStopError(ScrcpyError):
    """scrcpy停止异常"""
    def __init__(self, device_id: str, message: str = "停止失败"):
        super().__init__(device_id, message)

class ConfigError(Exception):
    """配置异常"""
    def __init__(self, message: str):
        self.message = message
        super().__init__(f"配置错误: {message}")

class APIError(Exception):
    """API异常"""
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message
        super().__init__(f"API错误 {status_code}: {message}")

class ValidationError(Exception):
    """验证异常"""
    def __init__(self, field: str, message: str):
        self.field = field
        self.message = message
        super().__init__(f"验证错误 {field}: {message}")

class ResourceNotFoundError(Exception):
    """资源未找到异常"""
    def __init__(self, resource_type: str, resource_id: str):
        self.resource_type = resource_type
        self.resource_id = resource_id
        super().__init__(f"{resource_type} {resource_id} 未找到")

class PermissionError(Exception):
    """权限异常"""
    def __init__(self, action: str, resource: str):
        self.action = action
        self.resource = resource
        super().__init__(f"无权限执行 {action} 操作: {resource}")

class RateLimitError(Exception):
    """频率限制异常"""
    def __init__(self, limit: int, window: int):
        self.limit = limit
        self.window = window
        super().__init__(f"请求频率超限: {limit} 次/{window} 秒")

class SystemError(Exception):
    """系统异常"""
    def __init__(self, component: str, message: str):
        self.component = component
        self.message = message
        super().__init__(f"系统错误 {component}: {message}")
