"""
性能监控和优化模块
"""

import asyncio
import psutil
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from collections import deque
import weakref

from config import get_config
from utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used: int
    memory_total: int
    disk_usage: float
    network_sent: int
    network_recv: int
    active_connections: int
    response_time: float

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        self.is_monitoring = False
        self.monitor_task = None
        self.alert_thresholds = {
            'cpu_percent': get_config('performance.cpu_threshold', 80),
            'memory_percent': get_config('performance.memory_threshold', 85),
            'disk_usage': get_config('performance.disk_threshold', 90),
            'response_time': get_config('performance.response_time_threshold', 5.0)
        }
        
    async def start_monitoring(self, interval: int = 5):
        """开始性能监控"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("性能监控已启动")
        
    async def stop_monitoring(self):
        """停止性能监控"""
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("性能监控已停止")
        
    async def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = await self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 检查告警
                await self._check_alerts(metrics)
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"性能监控错误: {e}")
                await asyncio.sleep(interval)
                
    async def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 网络统计
        network = psutil.net_io_counters()
        
        # 网络连接数
        connections = len(psutil.net_connections())
        
        # 响应时间（模拟）
        response_time = await self._measure_response_time()
        
        return PerformanceMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used=memory.used,
            memory_total=memory.total,
            disk_usage=disk.percent,
            network_sent=network.bytes_sent,
            network_recv=network.bytes_recv,
            active_connections=connections,
            response_time=response_time
        )
        
    async def _measure_response_time(self) -> float:
        """测量响应时间"""
        start_time = time.time()
        # 模拟一个简单的操作
        await asyncio.sleep(0.001)
        return time.time() - start_time
        
    async def _check_alerts(self, metrics: PerformanceMetrics):
        """检查告警"""
        alerts = []
        
        if metrics.cpu_percent > self.alert_thresholds['cpu_percent']:
            alerts.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
            
        if metrics.memory_percent > self.alert_thresholds['memory_percent']:
            alerts.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
            
        if metrics.disk_usage > self.alert_thresholds['disk_usage']:
            alerts.append(f"磁盘使用率过高: {metrics.disk_usage:.1f}%")
            
        if metrics.response_time > self.alert_thresholds['response_time']:
            alerts.append(f"响应时间过长: {metrics.response_time:.2f}s")
            
        for alert in alerts:
            logger.warning(f"性能告警: {alert}")
            
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        return self.metrics_history[-1] if self.metrics_history else None
        
    def get_metrics_history(self, hours: int = 1) -> List[PerformanceMetrics]:
        """获取历史性能指标"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            metrics for metrics in self.metrics_history
            if metrics.timestamp > cutoff_time
        ]
        
    def get_average_metrics(self, hours: int = 1) -> Dict[str, float]:
        """获取平均性能指标"""
        history = self.get_metrics_history(hours)
        if not history:
            return {}
            
        return {
            'cpu_percent': sum(m.cpu_percent for m in history) / len(history),
            'memory_percent': sum(m.memory_percent for m in history) / len(history),
            'disk_usage': sum(m.disk_usage for m in history) / len(history),
            'response_time': sum(m.response_time for m in history) / len(history),
            'active_connections': sum(m.active_connections for m in history) / len(history)
        }

class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        self.resource_pools = {}
        self.cleanup_tasks = []
        
    def create_pool(self, name: str, factory, max_size: int = 10):
        """创建资源池"""
        self.resource_pools[name] = {
            'factory': factory,
            'pool': asyncio.Queue(maxsize=max_size),
            'max_size': max_size,
            'created': 0
        }
        
    async def get_resource(self, pool_name: str):
        """获取资源"""
        if pool_name not in self.resource_pools:
            raise ValueError(f"资源池 {pool_name} 不存在")
            
        pool_info = self.resource_pools[pool_name]
        pool = pool_info['pool']
        
        try:
            # 尝试从池中获取资源
            resource = pool.get_nowait()
            return resource
        except asyncio.QueueEmpty:
            # 池为空，创建新资源
            if pool_info['created'] < pool_info['max_size']:
                resource = await pool_info['factory']()
                pool_info['created'] += 1
                return resource
            else:
                # 等待资源释放
                return await pool.get()
                
    async def return_resource(self, pool_name: str, resource):
        """归还资源"""
        if pool_name not in self.resource_pools:
            return
            
        pool = self.resource_pools[pool_name]['pool']
        try:
            pool.put_nowait(resource)
        except asyncio.QueueFull:
            # 池已满，直接丢弃资源
            pass
            
    def schedule_cleanup(self, coro, delay: float = 0):
        """调度清理任务"""
        async def cleanup_task():
            if delay > 0:
                await asyncio.sleep(delay)
            await coro
            
        task = asyncio.create_task(cleanup_task())
        self.cleanup_tasks.append(task)
        
        # 清理已完成的任务
        self.cleanup_tasks = [t for t in self.cleanup_tasks if not t.done()]
        
    async def cleanup_all(self):
        """清理所有资源"""
        # 等待所有清理任务完成
        if self.cleanup_tasks:
            await asyncio.gather(*self.cleanup_tasks, return_exceptions=True)
            
        # 清空资源池
        for pool_info in self.resource_pools.values():
            while not pool_info['pool'].empty():
                try:
                    resource = pool_info['pool'].get_nowait()
                    # 如果资源有close方法，调用它
                    if hasattr(resource, 'close'):
                        if asyncio.iscoroutinefunction(resource.close):
                            await resource.close()
                        else:
                            resource.close()
                except asyncio.QueueEmpty:
                    break

class RetryManager:
    """重试管理器"""
    
    @staticmethod
    async def retry_async(
        func,
        max_attempts: int = 3,
        delay: float = 1.0,
        backoff: float = 2.0,
        exceptions: tuple = (Exception,)
    ):
        """异步重试装饰器"""
        for attempt in range(max_attempts):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func()
                else:
                    return func()
            except exceptions as e:
                if attempt == max_attempts - 1:
                    raise e
                    
                wait_time = delay * (backoff ** attempt)
                logger.warning(f"操作失败，{wait_time:.1f}秒后重试 (尝试 {attempt + 1}/{max_attempts}): {e}")
                await asyncio.sleep(wait_time)
                
    @staticmethod
    def retry_sync(
        max_attempts: int = 3,
        delay: float = 1.0,
        backoff: float = 2.0,
        exceptions: tuple = (Exception,)
    ):
        """同步重试装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                for attempt in range(max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except exceptions as e:
                        if attempt == max_attempts - 1:
                            raise e
                            
                        wait_time = delay * (backoff ** attempt)
                        logger.warning(f"操作失败，{wait_time:.1f}秒后重试 (尝试 {attempt + 1}/{max_attempts}): {e}")
                        time.sleep(wait_time)
            return wrapper
        return decorator

class MemoryManager:
    """内存管理器"""
    
    def __init__(self):
        self.weak_refs = weakref.WeakSet()
        
    def register_object(self, obj):
        """注册对象用于内存监控"""
        self.weak_refs.add(obj)
        
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss': memory_info.rss,  # 物理内存
            'vms': memory_info.vms,  # 虚拟内存
            'percent': process.memory_percent(),
            'available': psutil.virtual_memory().available,
            'registered_objects': len(self.weak_refs)
        }
        
    def force_gc(self):
        """强制垃圾回收"""
        import gc
        collected = gc.collect()
        logger.info(f"垃圾回收完成，回收了 {collected} 个对象")
        return collected

# 全局实例
performance_monitor = PerformanceMonitor()
resource_manager = ResourceManager()
memory_manager = MemoryManager()

# 便捷函数
async def start_performance_monitoring():
    """启动性能监控"""
    await performance_monitor.start_monitoring()

async def stop_performance_monitoring():
    """停止性能监控"""
    await performance_monitor.stop_monitoring()

def get_system_metrics():
    """获取系统指标"""
    return {
        'performance': performance_monitor.get_current_metrics(),
        'memory': memory_manager.get_memory_usage(),
        'average_1h': performance_monitor.get_average_metrics(1)
    }
