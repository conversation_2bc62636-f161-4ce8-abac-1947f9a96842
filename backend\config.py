"""
配置管理模块
"""

import os
import json
from typing import Dict, Any, Optional
from pathlib import Path

class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config_path = Path(config_file)
        self._config: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        # 默认配置
        default_config = {
            "server": {
                "host": "0.0.0.0",
                "port": 8000,
                "reload": True,
                "log_level": "info"
            },
            "websocket": {
                "ping_interval": 30,
                "ping_timeout": 10,
                "max_connections": 100
            },
            "device": {
                "scan_interval": 5,
                "connection_timeout": 30,
                "max_retry_attempts": 3
            },
            "scrcpy": {
                "base_port": 8080,
                "max_size": 800,
                "bitrate": "2M",
                "max_fps": 30,
                "no_audio": True
            },
            "script": {
                "max_concurrent": 10,
                "execution_timeout": 300,
                "log_retention_days": 7
            },
            "paths": {
                "scripts_dir": "scripts",
                "data_dir": "data",
                "logs_dir": "logs",
                "temp_dir": "temp"
            },
            "security": {
                "allowed_origins": ["*"],
                "api_key_required": False,
                "rate_limit_enabled": False
            }
        }
        
        # 如果配置文件存在，加载并合并
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self._merge_config(default_config, file_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        self._config = default_config
        
        # 从环境变量覆盖配置
        self._load_from_env()
        
        # 保存配置文件（如果不存在）
        if not self.config_path.exists():
            self.save_config()
    
    def _merge_config(self, default: Dict[str, Any], override: Dict[str, Any]):
        """合并配置"""
        for key, value in override.items():
            if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                self._merge_config(default[key], value)
            else:
                default[key] = value
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        env_mappings = {
            "SERVER_HOST": ("server", "host"),
            "SERVER_PORT": ("server", "port"),
            "LOG_LEVEL": ("server", "log_level"),
            "SCRCPY_BASE_PORT": ("scrcpy", "base_port"),
            "SCRCPY_MAX_SIZE": ("scrcpy", "max_size"),
            "SCRCPY_BITRATE": ("scrcpy", "bitrate"),
            "SCRIPTS_DIR": ("paths", "scripts_dir"),
            "DATA_DIR": ("paths", "data_dir"),
        }
        
        for env_key, (section, key) in env_mappings.items():
            value = os.getenv(env_key)
            if value is not None:
                # 尝试转换类型
                if key == "port" or key == "base_port" or key == "max_size":
                    try:
                        value = int(value)
                    except ValueError:
                        continue
                
                if section not in self._config:
                    self._config[section] = {}
                self._config[section][key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def update(self, config: Dict[str, Any]):
        """更新配置"""
        self._merge_config(self._config, config)
        self.save_config()

# 全局配置实例
config = Config()

# 便捷访问函数
def get_config(key: str, default: Any = None) -> Any:
    """获取配置值"""
    return config.get(key, default)

def set_config(key: str, value: Any):
    """设置配置值"""
    config.set(key, value)

def save_config():
    """保存配置"""
    config.save_config()

# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        get_config("paths.scripts_dir", "scripts"),
        get_config("paths.data_dir", "data"),
        get_config("paths.logs_dir", "logs"),
        get_config("paths.temp_dir", "temp"),
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

# 初始化时创建目录
ensure_directories()
