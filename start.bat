@echo off
chcp 65001 >nul
echo ================================================
echo           设备群控系统启动脚本
echo ================================================
echo.

echo 检查环境...

:: 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)
echo ✅ Python已安装

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未添加到PATH
    pause
    exit /b 1
)
echo ✅ Node.js已安装

:: 检查ADB
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  ADB未安装或未添加到PATH，设备管理功能将无法使用
) else (
    echo ✅ ADB已安装
)

:: 检查scrcpy
scrcpy --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  scrcpy未安装或未添加到PATH，设备画面显示功能将无法使用
) else (
    echo ✅ scrcpy已安装
)

echo.
echo 启动服务...
echo.

:: 启动后端服务
echo 启动后端服务...
start "后端服务" cmd /k "cd backend && python start.py"

:: 等待后端启动
timeout /t 3 /nobreak >nul

:: 启动前端服务
echo 启动前端服务...
start "前端服务" cmd /k "cd frontend && npm run dev"

echo.
echo ================================================
echo 服务启动完成！
echo.
echo 后端服务: http://localhost:8000
echo 前端界面: http://localhost:5173
echo API文档: http://localhost:8000/docs
echo.
echo 按任意键退出...
echo ================================================
pause >nul
