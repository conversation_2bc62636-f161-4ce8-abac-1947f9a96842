# 抖音自动关注脚本示例
# 这个脚本演示了如何自动关注、点赞、收藏

import time
import random

# 脚本信息
script_name = "抖音自动关注脚本"
script_description = "自动关注用户、点赞视频、收藏视频"

log_info(f"开始执行 {script_name}")
log_info(f"目标设备: {', '.join(device_ids)}")

# 配置参数
VIDEO_COUNT = 5  # 要处理的视频数量
DELAY_BETWEEN_ACTIONS = 2  # 操作间隔时间(秒)
DELAY_BETWEEN_VIDEOS = 4  # 视频间隔时间(秒)

# 坐标配置
LIKE_BUTTON = (664, 900)     # 点赞按钮坐标
FOLLOW_BUTTON = (664, 823)   # 关注按钮坐标
COLLECT_BUTTON = (664, 1180) # 收藏按钮坐标
SWIPE_START = (528, 800)     # 滑动起始坐标
SWIPE_END = (528, 410)       # 滑动结束坐标

def auto_interact_videos(device_id, count=VIDEO_COUNT):
    """自动与视频互动（关注、点赞、收藏）"""
    log_info(f"设备 {device_id}: 开始自动互动 {count} 个视频")
    
    success_count = 0
    
    for i in range(count):
        try:
            log_info(f"设备 {device_id}: 正在处理第 {i+1} 个视频")
            
            # 等待视频加载
            time.sleep(DELAY_BETWEEN_VIDEOS)
            
            # 点赞
            click_phone(device_id, LIKE_BUTTON[0], LIKE_BUTTON[1], "点赞")
            log_info(f"设备 {device_id}: 已点赞第 {i+1} 个视频")
            time.sleep(DELAY_BETWEEN_ACTIONS)
            
            # 关注
            click_phone(device_id, FOLLOW_BUTTON[0], FOLLOW_BUTTON[1], "关注")
            log_info(f"设备 {device_id}: 已关注第 {i+1} 个视频的作者")
            time.sleep(DELAY_BETWEEN_ACTIONS)
            
            # 收藏
            click_phone(device_id, COLLECT_BUTTON[0], COLLECT_BUTTON[1], "收藏")
            log_info(f"设备 {device_id}: 已收藏第 {i+1} 个视频")
            time.sleep(DELAY_BETWEEN_ACTIONS)
            
            # 滑动到下一个视频 (除了最后一个)
            if i < count - 1:
                touch_swip(device_id, SWIPE_START[0], SWIPE_START[1], 
                          SWIPE_END[0], SWIPE_END[1], 200)
                log_info(f"设备 {device_id}: 已滑动到下一个视频")
            
            success_count += 1
            
        except Exception as e:
            log_error(f"设备 {device_id}: 处理第 {i+1} 个视频时出错: {str(e)}")
            continue
    
    log_success(f"设备 {device_id}: 完成自动互动，成功处理 {success_count}/{count} 个视频")
    return success_count

# 主执行逻辑
try:
    # 首先打开抖音应用
    for device_id in device_ids:
        log_info(f"设备 {device_id}: 正在打开抖音应用")
        open_app(device_id, "com.ss.android.ugc.aweme")
    
    # 等待应用启动
    log_info("等待抖音应用启动...")
    time.sleep(5)
    
    # 对每个设备执行自动互动
    total_success = 0
    for device_id in device_ids:
        success = auto_interact_videos(device_id)
        total_success += success
    
    log_success(f"脚本执行完成！总共成功处理 {total_success} 个视频")
    
except Exception as e:
    log_error(f"脚本执行失败: {str(e)}")
    raise

log_info(f"{script_name} 执行结束")
