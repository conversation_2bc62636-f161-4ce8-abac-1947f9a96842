<template>
  <div class="device-monitor">
    <div class="monitor-header">
      <h3 class="monitor-title">
        <el-icon><Monitor /></el-icon>
        设备性能监控
      </h3>
      <div class="monitor-controls">
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          @change="toggleAutoRefresh"
        />
        <el-button size="small" @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div class="monitor-content">
      <div class="device-grid">
        <div 
          v-for="device in monitoredDevices" 
          :key="device.id"
          class="device-card"
          :class="{ 'offline': device.status !== 'online' }"
        >
          <div class="device-header">
            <div class="device-info">
              <h4 class="device-name">{{ device.name || device.id }}</h4>
              <el-tag 
                :type="getStatusType(device.status)" 
                size="small"
                class="device-status"
              >
                {{ getStatusText(device.status) }}
              </el-tag>
            </div>
            <div class="device-actions">
              <el-dropdown @command="handleDeviceAction">
                <el-button size="small" circle>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'screenshot', device: device}">
                      <el-icon><Camera /></el-icon>
                      截屏
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'reboot', device: device}">
                      <el-icon><RefreshRight /></el-icon>
                      重启
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'details', device: device}">
                      <el-icon><View /></el-icon>
                      详情
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="performance-metrics" v-if="device.status === 'online'">
            <!-- CPU使用率 -->
            <div class="metric-item">
              <div class="metric-label">
                <el-icon><Cpu /></el-icon>
                CPU使用率
              </div>
              <div class="metric-value">
                <el-progress 
                  :percentage="device.performance?.cpu || 0"
                  :color="getProgressColor(device.performance?.cpu || 0)"
                  :show-text="false"
                  class="metric-progress"
                />
                <span class="metric-text">{{ device.performance?.cpu || 0 }}%</span>
              </div>
            </div>

            <!-- 内存使用率 -->
            <div class="metric-item">
              <div class="metric-label">
                <el-icon><Memo /></el-icon>
                内存使用率
              </div>
              <div class="metric-value">
                <el-progress 
                  :percentage="device.performance?.memory || 0"
                  :color="getProgressColor(device.performance?.memory || 0)"
                  :show-text="false"
                  class="metric-progress"
                />
                <span class="metric-text">{{ device.performance?.memory || 0 }}%</span>
              </div>
            </div>

            <!-- 电池电量 -->
            <div class="metric-item">
              <div class="metric-label">
                <el-icon><Battery /></el-icon>
                电池电量
              </div>
              <div class="metric-value">
                <el-progress 
                  :percentage="device.performance?.battery || 0"
                  :color="getBatteryColor(device.performance?.battery || 0)"
                  :show-text="false"
                  class="metric-progress"
                />
                <span class="metric-text">{{ device.performance?.battery || 0 }}%</span>
              </div>
            </div>

            <!-- 存储空间 -->
            <div class="metric-item">
              <div class="metric-label">
                <el-icon><FolderOpened /></el-icon>
                存储使用率
              </div>
              <div class="metric-value">
                <el-progress 
                  :percentage="device.performance?.storage || 0"
                  :color="getProgressColor(device.performance?.storage || 0)"
                  :show-text="false"
                  class="metric-progress"
                />
                <span class="metric-text">{{ device.performance?.storage || 0 }}%</span>
              </div>
            </div>

            <!-- 网络状态 -->
            <div class="metric-item">
              <div class="metric-label">
                <el-icon><Wifi /></el-icon>
                网络状态
              </div>
              <div class="metric-value">
                <el-tag 
                  :type="device.performance?.network === 'wifi' ? 'success' : 'warning'"
                  size="small"
                >
                  {{ getNetworkText(device.performance?.network) }}
                </el-tag>
              </div>
            </div>

            <!-- 温度 -->
            <div class="metric-item">
              <div class="metric-label">
                <el-icon><Thermometer /></el-icon>
                设备温度
              </div>
              <div class="metric-value">
                <span class="metric-text temperature" :class="getTemperatureClass(device.performance?.temperature)">
                  {{ device.performance?.temperature || 0 }}°C
                </span>
              </div>
            </div>
          </div>

          <div class="offline-message" v-else>
            <el-icon><WarningFilled /></el-icon>
            <span>设备离线</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备详情对话框 -->
    <el-dialog
      v-model="detailsVisible"
      :title="`设备详情 - ${selectedDevice?.name || selectedDevice?.id}`"
      width="600px"
    >
      <div class="device-details" v-if="selectedDevice">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备ID">{{ selectedDevice.id }}</el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ selectedDevice.name || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedDevice.status)">
              {{ getStatusText(selectedDevice.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="型号">{{ selectedDevice.model || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="Android版本">{{ selectedDevice.android_version || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="分辨率">{{ selectedDevice.resolution || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="CPU使用率">{{ selectedDevice.performance?.cpu || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="内存使用率">{{ selectedDevice.performance?.memory || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="电池电量">{{ selectedDevice.performance?.battery || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="存储使用率">{{ selectedDevice.performance?.storage || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="网络状态">{{ getNetworkText(selectedDevice.performance?.network) }}</el-descriptions-item>
          <el-descriptions-item label="设备温度">{{ selectedDevice.performance?.temperature || 0 }}°C</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Monitor, Refresh, MoreFilled, Camera, RefreshRight, View, 
  Cpu, Memo, Battery, FolderOpened, Wifi, Thermometer, WarningFilled 
} from '@element-plus/icons-vue';

export default {
  name: 'DeviceMonitor',
  components: {
    Monitor, Refresh, MoreFilled, Camera, RefreshRight, View,
    Cpu, Memo, Battery, FolderOpened, Wifi, Thermometer, WarningFilled
  },
  props: {
    devices: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const loading = ref(false);
    const autoRefresh = ref(true);
    const refreshInterval = ref(null);
    const detailsVisible = ref(false);
    const selectedDevice = ref(null);

    // 监控的设备数据（包含性能信息）
    const monitoredDevices = ref([]);

    // 初始化设备监控数据
    const initializeDevices = () => {
      monitoredDevices.value = props.devices.map(device => ({
        ...device,
        performance: generateMockPerformance()
      }));
    };

    // 生成模拟性能数据
    const generateMockPerformance = () => ({
      cpu: Math.floor(Math.random() * 100),
      memory: Math.floor(Math.random() * 100),
      battery: Math.floor(Math.random() * 100),
      storage: Math.floor(Math.random() * 100),
      network: Math.random() > 0.5 ? 'wifi' : 'mobile',
      temperature: Math.floor(Math.random() * 20) + 30 // 30-50°C
    });

    // 刷新性能数据
    const refreshData = async () => {
      loading.value = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        monitoredDevices.value.forEach(device => {
          if (device.status === 'online') {
            device.performance = generateMockPerformance();
          }
        });
        
        ElMessage.success('性能数据已更新');
      } catch (error) {
        ElMessage.error('更新失败: ' + error.message);
      } finally {
        loading.value = false;
      }
    };

    // 切换自动刷新
    const toggleAutoRefresh = (enabled) => {
      if (enabled) {
        refreshInterval.value = setInterval(refreshData, 5000); // 每5秒刷新
      } else {
        if (refreshInterval.value) {
          clearInterval(refreshInterval.value);
          refreshInterval.value = null;
        }
      }
    };

    // 处理设备操作
    const handleDeviceAction = async (command) => {
      const { action, device } = command;
      
      switch (action) {
        case 'screenshot':
          ElMessage.info(`正在为设备 ${device.name || device.id} 截屏...`);
          break;
        case 'reboot':
          try {
            await ElMessageBox.confirm(
              `确定要重启设备 ${device.name || device.id} 吗？`,
              '确认重启',
              { type: 'warning' }
            );
            ElMessage.success('重启命令已发送');
          } catch {
            // 用户取消
          }
          break;
        case 'details':
          selectedDevice.value = device;
          detailsVisible.value = true;
          break;
      }
    };

    // 获取状态类型
    const getStatusType = (status) => {
      const statusMap = {
        'online': 'success',
        'offline': 'danger',
        'connecting': 'warning',
        'error': 'danger'
      };
      return statusMap[status] || 'info';
    };

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        'online': '在线',
        'offline': '离线',
        'connecting': '连接中',
        'error': '错误'
      };
      return statusMap[status] || '未知';
    };

    // 获取进度条颜色
    const getProgressColor = (percentage) => {
      if (percentage < 50) return '#67c23a';
      if (percentage < 80) return '#e6a23c';
      return '#f56c6c';
    };

    // 获取电池颜色
    const getBatteryColor = (percentage) => {
      if (percentage > 50) return '#67c23a';
      if (percentage > 20) return '#e6a23c';
      return '#f56c6c';
    };

    // 获取网络状态文本
    const getNetworkText = (network) => {
      const networkMap = {
        'wifi': 'WiFi',
        'mobile': '移动网络',
        'none': '无网络'
      };
      return networkMap[network] || '未知';
    };

    // 获取温度样式类
    const getTemperatureClass = (temperature) => {
      if (temperature > 45) return 'hot';
      if (temperature > 40) return 'warm';
      return 'normal';
    };

    // 生命周期
    onMounted(() => {
      initializeDevices();
      if (autoRefresh.value) {
        toggleAutoRefresh(true);
      }
    });

    onUnmounted(() => {
      if (refreshInterval.value) {
        clearInterval(refreshInterval.value);
      }
    });

    return {
      loading,
      autoRefresh,
      detailsVisible,
      selectedDevice,
      monitoredDevices,
      refreshData,
      toggleAutoRefresh,
      handleDeviceAction,
      getStatusType,
      getStatusText,
      getProgressColor,
      getBatteryColor,
      getNetworkText,
      getTemperatureClass
    };
  }
};
</script>

<style scoped>
.device-monitor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.monitor-header {
  background: white;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitor-title {
  margin: 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
}

.monitor-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.monitor-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.device-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.device-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.device-card.offline {
  border-color: #f56c6c;
  background: #fef0f0;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.device-info {
  flex: 1;
}

.device-name {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.device-status {
  font-size: 12px;
}

.device-actions {
  margin-left: 12px;
}

.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.metric-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  min-width: 100px;
}

.metric-value {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.metric-progress {
  flex: 1;
  max-width: 120px;
}

.metric-text {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  min-width: 40px;
  text-align: right;
}

.temperature {
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
}

.temperature.normal {
  background: #f0f9ff;
  color: #0369a1;
}

.temperature.warm {
  background: #fef3c7;
  color: #d97706;
}

.temperature.hot {
  background: #fee2e2;
  color: #dc2626;
}

.offline-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px 20px;
  color: #909399;
  font-size: 14px;
}

.device-details {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-grid {
    grid-template-columns: 1fr;
  }

  .monitor-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .monitor-controls {
    justify-content: space-between;
  }

  .metric-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .metric-value {
    width: 100%;
  }

  .metric-progress {
    max-width: none;
  }
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.device-card.offline .offline-message {
  animation: pulse 2s infinite;
}

/* 进度条自定义样式 */
.metric-progress :deep(.el-progress-bar__outer) {
  background-color: #f0f2f5;
  border-radius: 6px;
  height: 8px;
}

.metric-progress :deep(.el-progress-bar__inner) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

/* 下拉菜单样式 */
.device-actions :deep(.el-dropdown) {
  cursor: pointer;
}

.device-actions :deep(.el-button) {
  border: 1px solid #dcdfe6;
  background: #f8f9fa;
}

.device-actions :deep(.el-button:hover) {
  border-color: #409eff;
  background: #ecf5ff;
  color: #409eff;
}
</style>
