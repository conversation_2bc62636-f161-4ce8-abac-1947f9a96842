"""
主后端服务器 - 基于FastAPI的设备群控系统
提供设备管理、脚本执行、WebSocket通信等功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import asyncio
import json
import logging
from typing import List, Dict, Any
from datetime import datetime
import uvicorn

from models.device import Device, DeviceStatus, TouchEvent, KeyEvent, TextInput, AppOperation
from models.script import Script, ScriptStatus
from services.device_manager import DeviceManager
from services.script_manager import ScriptManager
from services.scrcpy_manager import ScrcpyManager
from utils.websocket_manager import WebSocketManager
from routes.auth import router as auth_router

# 导入配置和日志
from config import get_config
from utils.logger import get_logger
from utils.exceptions import *

# 配置日志
logger = get_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="设备群控系统API",
    description="基于ADB和scrcpy的Android设备群控系统",
    version="1.0.0"
)

# 注册路由
app.include_router(auth_router)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=get_config("security.allowed_origins", ["*"]),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化管理器
device_manager = DeviceManager()
script_manager = ScriptManager()
scrcpy_manager = ScrcpyManager()
websocket_manager = WebSocketManager()

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("启动设备群控系统...")
    
    # 初始化设备管理器
    await device_manager.initialize()
    
    # 启动设备监控任务
    asyncio.create_task(device_monitor_task())
    
    logger.info("系统启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("关闭设备群控系统...")
    
    # 停止所有scrcpy进程
    await scrcpy_manager.stop_all()
    
    # 停止所有脚本
    await script_manager.stop_all_scripts()
    
    logger.info("系统关闭完成")

# API端点
@app.get("/")
async def root():
    """根路径"""
    return {"message": "设备群控系统API", "version": "1.0.0"}

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "services": {
            "device_manager": "running",
            "script_manager": "running",
            "scrcpy_manager": "running",
            "websocket": "running"
        }
    }

# WebSocket连接管理
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理不同类型的消息
            await handle_websocket_message(websocket, message)
            
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        websocket_manager.disconnect(websocket)

async def handle_websocket_message(websocket: WebSocket, message: Dict[str, Any]):
    """处理WebSocket消息"""
    message_type = message.get("type")
    data = message.get("data", {})
    
    if message_type == "ping":
        await websocket.send_text(json.dumps({"type": "pong"}))
    
    elif message_type == "start_scrcpy":
        device_id = data.get("device_id")
        if device_id:
            success = await scrcpy_manager.start_scrcpy(device_id)
            await websocket.send_text(json.dumps({
                "type": "scrcpy_status",
                "data": {"device_id": device_id, "status": "started" if success else "failed"}
            }))
    
    elif message_type == "stop_scrcpy":
        device_id = data.get("device_id")
        if device_id:
            await scrcpy_manager.stop_scrcpy(device_id)
            await websocket.send_text(json.dumps({
                "type": "scrcpy_status",
                "data": {"device_id": device_id, "status": "stopped"}
            }))

# 设备管理API
@app.get("/api/devices", response_model=List[Device])
async def get_devices():
    """获取所有设备列表"""
    return await device_manager.get_all_devices()

@app.post("/api/devices/scan")
async def scan_devices():
    """扫描ADB设备"""
    devices = await device_manager.scan_devices()
    
    # 通过WebSocket广播设备更新
    await websocket_manager.broadcast({
        "type": "devices_updated",
        "data": [device.dict() for device in devices]
    })
    
    return {"message": "设备扫描完成", "count": len(devices)}

@app.post("/api/devices/{device_id}/connect")
async def connect_device(device_id: str):
    """连接设备"""
    success = await device_manager.connect_device(device_id)
    if not success:
        raise HTTPException(status_code=400, detail="设备连接失败")
    
    # 广播设备状态更新
    device = await device_manager.get_device(device_id)
    await websocket_manager.broadcast({
        "type": "device_status_changed",
        "data": device.dict() if device else None
    })
    
    return {"message": "设备连接成功"}

@app.post("/api/devices/{device_id}/disconnect")
async def disconnect_device(device_id: str):
    """断开设备连接"""
    success = await device_manager.disconnect_device(device_id)
    if not success:
        raise HTTPException(status_code=400, detail="设备断开失败")
    
    # 停止该设备的scrcpy
    await scrcpy_manager.stop_scrcpy(device_id)
    
    # 广播设备状态更新
    device = await device_manager.get_device(device_id)
    await websocket_manager.broadcast({
        "type": "device_status_changed",
        "data": device.dict() if device else None
    })
    
    return {"message": "设备断开成功"}

# 脚本管理API
@app.get("/api/scripts", response_model=List[Script])
async def get_scripts():
    """获取所有脚本列表"""
    return await script_manager.get_all_scripts()

@app.post("/api/scripts")
async def create_script(script_data: dict):
    """创建新脚本"""
    script = await script_manager.create_script(
        name=script_data["name"],
        content=script_data["content"],
        description=script_data.get("description", "")
    )
    return script.dict()

@app.put("/api/scripts/{script_id}")
async def update_script(script_id: str, script_data: dict):
    """更新脚本"""
    script = await script_manager.update_script(
        script_id=script_id,
        name=script_data.get("name"),
        content=script_data.get("content"),
        description=script_data.get("description")
    )
    if not script:
        raise HTTPException(status_code=404, detail="脚本不存在")
    return script.dict()

@app.delete("/api/scripts/{script_id}")
async def delete_script(script_id: str):
    """删除脚本"""
    success = await script_manager.delete_script(script_id)
    if not success:
        raise HTTPException(status_code=404, detail="脚本不存在")
    return {"message": "脚本删除成功"}

@app.post("/api/scripts/{script_id}/run")
async def run_script(script_id: str, run_data: dict):
    """运行脚本"""
    device_ids = run_data.get("device_ids", [])
    if not device_ids:
        raise HTTPException(status_code=400, detail="请选择要运行脚本的设备")
    
    # 启动脚本执行任务
    task_id = await script_manager.run_script(script_id, device_ids)
    
    return {"message": "脚本开始执行", "task_id": task_id}

@app.post("/api/scripts/{script_id}/stop")
async def stop_script(script_id: str):
    """停止脚本"""
    success = await script_manager.stop_script(script_id)
    if not success:
        raise HTTPException(status_code=404, detail="脚本不存在或未在运行")

    return {"message": "脚本已停止"}

# 设备控制API
@app.post("/api/devices/{device_id}/touch")
async def device_touch(device_id: str, touch_data: dict):
    """设备触摸操作"""
    touch_event = TouchEvent(
        device_id=device_id,
        x=touch_data["x"],
        y=touch_data["y"],
        action=touch_data.get("action", "tap"),
        duration=touch_data.get("duration"),
        end_x=touch_data.get("end_x"),
        end_y=touch_data.get("end_y")
    )

    response = await device_manager.execute_touch(touch_event)

    # 广播操作结果
    await websocket_manager.broadcast({
        "type": "device_operation",
        "data": response.dict()
    })

    return response.dict()

@app.post("/api/devices/{device_id}/key")
async def device_key(device_id: str, key_data: dict):
    """设备按键操作"""
    key_event = KeyEvent(
        device_id=device_id,
        key_code=key_data["key_code"],
        action=key_data.get("action", "press")
    )

    response = await device_manager.execute_key(key_event)

    # 广播操作结果
    await websocket_manager.broadcast({
        "type": "device_operation",
        "data": response.dict()
    })

    return response.dict()

@app.post("/api/devices/{device_id}/input")
async def device_input(device_id: str, input_data: dict):
    """设备文本输入"""
    text_input = TextInput(
        device_id=device_id,
        text=input_data["text"],
        clear_before=input_data.get("clear_before", False)
    )

    response = await device_manager.input_text(text_input)

    # 广播操作结果
    await websocket_manager.broadcast({
        "type": "device_operation",
        "data": response.dict()
    })

    return response.dict()

@app.post("/api/devices/{device_id}/app")
async def device_app_operation(device_id: str, app_data: dict):
    """设备应用操作"""
    app_operation = AppOperation(
        device_id=device_id,
        package_name=app_data["package_name"],
        action=app_data["action"],
        apk_path=app_data.get("apk_path")
    )

    response = await device_manager.manage_app(app_operation)

    # 广播操作结果
    await websocket_manager.broadcast({
        "type": "device_operation",
        "data": response.dict()
    })

    return response.dict()

@app.get("/api/devices/{device_id}/apps")
async def get_device_apps(device_id: str):
    """获取设备应用列表"""
    response = await device_manager.get_installed_apps(device_id)
    return response.dict()

@app.post("/api/devices/{device_id}/screenshot")
async def take_device_screenshot(device_id: str, screenshot_data: dict = None):
    """设备截屏"""
    save_path = screenshot_data.get("save_path") if screenshot_data else None
    response = await device_manager.take_screenshot(device_id, save_path)
    return response.dict()

# 设备监控任务
async def device_monitor_task():
    """定期监控设备状态"""
    while True:
        try:
            # 检查设备状态
            devices = await device_manager.check_device_status()
            
            # 如果有状态变化，广播更新
            if devices:
                await websocket_manager.broadcast({
                    "type": "devices_updated",
                    "data": [device.dict() for device in devices]
                })
            
            # 每5秒检查一次
            await asyncio.sleep(5)
            
        except Exception as e:
            logger.error(f"设备监控任务错误: {e}")
            await asyncio.sleep(10)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=get_config("server.host", "0.0.0.0"),
        port=get_config("server.port", 8000),
        reload=get_config("server.reload", True),
        log_level=get_config("server.log_level", "info")
    )
