"""
脚本数据模型
"""

from pydantic import BaseModel
from enum import Enum
from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid

class ScriptStatus(str, Enum):
    """脚本状态枚举"""
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    COMPLETED = "completed"

class ScriptType(str, Enum):
    """脚本类型枚举"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    SHELL = "shell"

class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"

class Script(BaseModel):
    """脚本模型"""
    id: str = None  # 脚本ID
    name: str  # 脚本名称
    description: Optional[str] = ""  # 脚本描述
    content: str  # 脚本内容
    script_type: ScriptType = ScriptType.PYTHON  # 脚本类型
    status: ScriptStatus = ScriptStatus.STOPPED  # 脚本状态
    created_at: datetime = None  # 创建时间
    updated_at: datetime = None  # 更新时间
    last_run_at: Optional[datetime] = None  # 最后运行时间
    run_count: int = 0  # 运行次数
    success_count: int = 0  # 成功次数
    error_count: int = 0  # 错误次数
    total_runtime: float = 0.0  # 总运行时间(秒)
    average_runtime: float = 0.0  # 平均运行时间(秒)
    parameters: Dict[str, Any] = {}  # 脚本参数
    tags: List[str] = []  # 标签
    author: Optional[str] = None  # 作者
    version: str = "1.0.0"  # 版本
    
    def __init__(self, **data):
        if not data.get('id'):
            data['id'] = str(uuid.uuid4())
        if not data.get('created_at'):
            data['created_at'] = datetime.now()
        if not data.get('updated_at'):
            data['updated_at'] = datetime.now()
        super().__init__(**data)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class ScriptExecution(BaseModel):
    """脚本执行记录模型"""
    id: str = None  # 执行ID
    script_id: str  # 脚本ID
    device_ids: List[str]  # 执行设备列表
    status: ScriptStatus = ScriptStatus.RUNNING  # 执行状态
    started_at: datetime = None  # 开始时间
    finished_at: Optional[datetime] = None  # 结束时间
    runtime: Optional[float] = None  # 运行时间(秒)
    success_devices: List[str] = []  # 成功设备列表
    error_devices: List[str] = []  # 错误设备列表
    logs: List['ScriptLog'] = []  # 执行日志
    result: Optional[Dict[str, Any]] = None  # 执行结果
    error_message: Optional[str] = None  # 错误信息
    
    def __init__(self, **data):
        if not data.get('id'):
            data['id'] = str(uuid.uuid4())
        if not data.get('started_at'):
            data['started_at'] = datetime.now()
        super().__init__(**data)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class ScriptLog(BaseModel):
    """脚本日志模型"""
    id: str = None  # 日志ID
    execution_id: str  # 执行ID
    script_id: str  # 脚本ID
    device_id: Optional[str] = None  # 设备ID
    level: LogLevel = LogLevel.INFO  # 日志级别
    message: str  # 日志消息
    timestamp: datetime = None  # 时间戳
    line_number: Optional[int] = None  # 代码行号
    function_name: Optional[str] = None  # 函数名
    extra_data: Optional[Dict[str, Any]] = None  # 额外数据
    
    def __init__(self, **data):
        if not data.get('id'):
            data['id'] = str(uuid.uuid4())
        if not data.get('timestamp'):
            data['timestamp'] = datetime.now()
        super().__init__(**data)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class ScriptTemplate(BaseModel):
    """脚本模板模型"""
    id: str = None  # 模板ID
    name: str  # 模板名称
    description: Optional[str] = ""  # 模板描述
    content: str  # 模板内容
    script_type: ScriptType = ScriptType.PYTHON  # 脚本类型
    category: str = "general"  # 分类
    parameters: List[Dict[str, Any]] = []  # 参数定义
    tags: List[str] = []  # 标签
    author: Optional[str] = None  # 作者
    version: str = "1.0.0"  # 版本
    created_at: datetime = None  # 创建时间
    
    def __init__(self, **data):
        if not data.get('id'):
            data['id'] = str(uuid.uuid4())
        if not data.get('created_at'):
            data['created_at'] = datetime.now()
        super().__init__(**data)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class ScriptSchedule(BaseModel):
    """脚本调度模型"""
    id: str = None  # 调度ID
    script_id: str  # 脚本ID
    device_ids: List[str]  # 设备ID列表
    schedule_type: str = "once"  # 调度类型: once, interval, cron
    schedule_config: Dict[str, Any] = {}  # 调度配置
    enabled: bool = True  # 是否启用
    next_run_time: Optional[datetime] = None  # 下次运行时间
    last_run_time: Optional[datetime] = None  # 上次运行时间
    run_count: int = 0  # 运行次数
    created_at: datetime = None  # 创建时间
    
    def __init__(self, **data):
        if not data.get('id'):
            data['id'] = str(uuid.uuid4())
        if not data.get('created_at'):
            data['created_at'] = datetime.now()
        super().__init__(**data)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
