// API服务模块 - 与后端API通信
const API_BASE_URL = 'http://localhost:8000/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // 通用请求方法
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body);
    }

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }
      
      return await response.text();
    } catch (error) {
      console.error(`API请求失败 [${endpoint}]:`, error);
      throw error;
    }
  }

  // GET请求
  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' });
  }

  // POST请求
  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: data,
    });
  }

  // PUT请求
  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: data,
    });
  }

  // DELETE请求
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // 设备相关API
  async getDevices() {
    return this.get('/devices');
  }

  async scanDevices() {
    return this.post('/devices/scan');
  }

  async connectDevice(deviceId) {
    return this.post(`/devices/${deviceId}/connect`);
  }

  async disconnectDevice(deviceId) {
    return this.post(`/devices/${deviceId}/disconnect`);
  }

  // 设备控制API
  async deviceTouch(deviceId, touchData) {
    return this.post(`/devices/${deviceId}/touch`, touchData);
  }

  async deviceKey(deviceId, keyData) {
    return this.post(`/devices/${deviceId}/key`, keyData);
  }

  async deviceInput(deviceId, inputData) {
    return this.post(`/devices/${deviceId}/input`, inputData);
  }

  async deviceAppOperation(deviceId, appData) {
    return this.post(`/devices/${deviceId}/app`, appData);
  }

  async getDeviceApps(deviceId) {
    return this.get(`/devices/${deviceId}/apps`);
  }

  async takeScreenshot(deviceId, screenshotData = {}) {
    return this.post(`/devices/${deviceId}/screenshot`, screenshotData);
  }

  // 脚本相关API
  async getScripts() {
    return this.get('/scripts');
  }

  async createScript(scriptData) {
    return this.post('/scripts', scriptData);
  }

  async updateScript(scriptId, scriptData) {
    return this.put(`/scripts/${scriptId}`, scriptData);
  }

  async deleteScript(scriptId) {
    return this.delete(`/scripts/${scriptId}`);
  }

  async runScript(scriptId, runData) {
    return this.post(`/scripts/${scriptId}/run`, runData);
  }

  async stopScript(scriptId) {
    return this.post(`/scripts/${scriptId}/stop`);
  }

  // 便捷方法 - 抖音相关操作
  async douyinLike(deviceId, x = 664, y = 900) {
    return this.deviceTouch(deviceId, {
      x,
      y,
      action: 'tap'
    });
  }

  async douyinCollect(deviceId, x = 664, y = 1180) {
    return this.deviceTouch(deviceId, {
      x,
      y,
      action: 'tap'
    });
  }

  async douyinFollow(deviceId, x = 664, y = 823) {
    return this.deviceTouch(deviceId, {
      x,
      y,
      action: 'tap'
    });
  }

  async douyinSwipeNext(deviceId, startX = 528, startY = 800, endX = 528, endY = 410, duration = 200) {
    return this.deviceTouch(deviceId, {
      x: startX,
      y: startY,
      end_x: endX,
      end_y: endY,
      action: 'swipe',
      duration
    });
  }

  async douyinSearch(deviceId, searchText) {
    // 点击搜索框
    await this.deviceTouch(deviceId, { x: 660, y: 120, action: 'tap' });
    
    // 输入搜索文本
    await this.deviceInput(deviceId, { text: searchText });
    
    // 点击搜索按钮
    await this.deviceTouch(deviceId, { x: 660, y: 120, action: 'tap' });
  }

  async douyinComment(deviceId, commentText) {
    // 点击评论按钮
    await this.deviceTouch(deviceId, { x: 660, y: 950, action: 'tap' });
    
    // 等待评论界面加载
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 点击评论输入框
    await this.deviceTouch(deviceId, { x: 200, y: 1550, action: 'tap' });
    
    // 输入评论
    await this.deviceInput(deviceId, { text: commentText });
    
    // 发送评论
    await this.deviceTouch(deviceId, { x: 660, y: 910, action: 'tap' });
    
    // 返回
    await this.deviceTouch(deviceId, { x: 360, y: 200, action: 'tap' });
  }

  async openDouyin(deviceId) {
    return this.deviceAppOperation(deviceId, {
      package_name: 'com.ss.android.ugc.aweme',
      action: 'start'
    });
  }

  async closeDouyin(deviceId) {
    return this.deviceAppOperation(deviceId, {
      package_name: 'com.ss.android.ugc.aweme',
      action: 'stop'
    });
  }
}

// 创建API服务实例
const apiService = new ApiService();

// 导出API服务
export default apiService;

// 导出便捷方法
export const {
  getDevices,
  scanDevices,
  connectDevice,
  disconnectDevice,
  deviceTouch,
  deviceKey,
  deviceInput,
  deviceAppOperation,
  getDeviceApps,
  takeScreenshot,
  getScripts,
  createScript,
  updateScript,
  deleteScript,
  runScript,
  stopScript,
  douyinLike,
  douyinCollect,
  douyinFollow,
  douyinSwipeNext,
  douyinSearch,
  douyinComment,
  openDouyin,
  closeDouyin
} = apiService;
