<template>
  <div class="device-manager">
    <div class="manager-header">
      <h3>设备管理</h3>
      <el-button type="primary" size="small" @click="scanDevices">扫描设备</el-button>
    </div>
    
    <div class="device-stats">
      <div class="stat-item">
        <span class="stat-label">已连接设备:</span>
        <span class="stat-value">{{ connectedDevices.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">在线设备:</span>
        <span class="stat-value online">{{ onlineDevices.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">离线设备:</span>
        <span class="stat-value offline">{{ offlineDevices.length }}</span>
      </div>
    </div>
    
    <div class="device-list">
      <div v-if="connectedDevices.length === 0" class="no-devices">
        <el-empty description="暂无设备连接"></el-empty>
      </div>
      
      <el-checkbox-group v-model="selectedDevices" class="device-checkbox-group">
        <!-- 在线设备 -->
        <el-checkbox 
          v-for="device in onlineDevices" 
          :key="device.id"
          :label="device.id"
          class="device-item"
        >
          <div class="device-info">
            <div class="device-name">{{ device.name || device.id }}</div>
            <div class="device-status" :class="device.status">
              <i class="el-icon-circle-check" v-if="device.status === 'online'"></i>
              <i class="el-icon-circle-close" v-else></i>
              {{ device.status === 'online' ? '在线' : '离线' }}
            </div>
          </div>
        </el-checkbox>
        
        <!-- 离线设备 -->
        <el-checkbox 
          v-for="device in offlineDevices" 
          :key="device.id"
          :label="device.id"
          class="device-item"
        >
          <div class="device-info">
            <div class="device-name">{{ device.name || device.id }}</div>
            <div class="device-status" :class="device.status">
              <i class="el-icon-circle-check" v-if="device.status === 'online'"></i>
              <i class="el-icon-circle-close" v-else></i>
              {{ device.status === 'online' ? '在线' : '离线' }}
            </div>
          </div>
        </el-checkbox>
      </el-checkbox-group>
    </div>
    
    <div class="device-actions">
      <el-button type="primary" @click="connectSelectedDevices" :disabled="selectedDevices.length === 0">
        连接选中设备
      </el-button>
      <el-button @click="disconnectSelectedDevices" :disabled="selectedDevices.length === 0">
        断开连接
      </el-button>
      <el-button @click="refreshDevices">刷新设备列表</el-button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useWebSocket } from '../utils/websocket';
import apiService from '../utils/api';

export default {
  name: 'DeviceManager',
  props: {
    // 接收父组件传递的设备列表
    devices: {
      type: Array,
      default: () => []
    }
  },
  setup(props, { emit }) {
    const connectedDevices = ref([]);
    const selectedDevices = ref([]);
    
    // 监听父组件传递的设备列表变化
    watch(() => props.devices, (newDevices) => {
      if (newDevices && newDevices.length > 0) {
        connectedDevices.value = [...newDevices];
      }
    }, { immediate: true });
    
    // 计算在线和离线设备
    const onlineDevices = computed(() => 
      connectedDevices.value.filter(device => device.status === 'online')
    );
    
    const offlineDevices = computed(() => 
      connectedDevices.value.filter(device => device.status === 'offline')
    );
    
    // 扫描设备
    const scanDevices = async () => {
      try {
        console.log('扫描设备中...');
        const result = await apiService.scanDevices();
        console.log('设备扫描结果:', result);

        // 获取最新设备列表
        const devices = await apiService.getDevices();
        connectedDevices.value = devices;

        // 通知父组件设备列表已更新
        emit('update:devices', devices);
      } catch (error) {
        console.error('扫描设备失败:', error);
        // 可以在这里显示错误提示
      }
    };
    
    // 连接选中设备
    const connectSelectedDevices = async () => {
      try {
        for (const deviceId of selectedDevices.value) {
          console.log(`连接设备: ${deviceId}`);
          await apiService.connectDevice(deviceId);
        }

        // 刷新设备列表
        await refreshDevices();
      } catch (error) {
        console.error('连接设备失败:', error);
      }
    };

    // 断开选中设备连接
    const disconnectSelectedDevices = async () => {
      try {
        for (const deviceId of selectedDevices.value) {
          console.log(`断开设备: ${deviceId}`);
          await apiService.disconnectDevice(deviceId);
        }

        // 刷新设备列表
        await refreshDevices();
      } catch (error) {
        console.error('断开设备失败:', error);
      }
    };
    
    // 刷新设备列表
    const refreshDevices = async () => {
      await scanDevices();
    };
    
    // 初始化时扫描设备
    onMounted(() => {
      scanDevices();
    });
    
    return {
      connectedDevices,
      selectedDevices,
      onlineDevices,
      offlineDevices,
      scanDevices,
      connectSelectedDevices,
      disconnectSelectedDevices,
      refreshDevices
    };
  }
};
</script>

<style scoped>
.device-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.manager-header h3 {
  margin: 0;
  color: #303133;
}

.device-stats {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-value {
  font-weight: bold;
  color: #303133;
}

.stat-value.online {
  color: #67c23a;
}

.stat-value.offline {
  color: #f56c6c;
}

.device-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
}

.no-devices {
  text-align: center;
  padding: 40px 20px;
}

.device-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-item {
  display: block;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

.device-item:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.device-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-name {
  font-weight: 500;
  color: #303133;
}

.device-status {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.device-status.online {
  color: #67c23a;
}

.device-status.offline {
  color: #f56c6c;
}

.device-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>