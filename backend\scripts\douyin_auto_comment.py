# 抖音自动评论脚本示例
# 这个脚本演示了如何自动评论视频

import time
import random

# 脚本信息
script_name = "抖音自动评论脚本"
script_description = "自动为抖音视频添加评论"

log_info(f"开始执行 {script_name}")
log_info(f"目标设备: {', '.join(device_ids)}")

# 配置参数
VIDEO_COUNT = 3  # 要评论的视频数量
DELAY_BETWEEN_ACTIONS = 2  # 操作间隔时间(秒)
DELAY_BETWEEN_VIDEOS = 4  # 视频间隔时间(秒)

# 坐标配置
COMMENT_BUTTON = (660, 950)   # 评论按钮坐标
COMMENT_INPUT = (200, 1550)   # 评论输入框坐标
SEND_BUTTON = (660, 910)      # 发送按钮坐标
BACK_BUTTON = (360, 200)      # 返回按钮坐标
SWIPE_START = (528, 800)      # 滑动起始坐标
SWIPE_END = (528, 410)        # 滑动结束坐标

# 评论内容库
COMMENTS = [
    "太棒了！👍",
    "很有意思呢",
    "学到了新知识",
    "支持支持！",
    "赞一个",
    "很不错的内容",
    "继续加油！",
    "太有才了",
    "很有创意",
    "期待更多作品"
]

def auto_comment_videos(device_id, count=VIDEO_COUNT):
    """自动评论视频"""
    log_info(f"设备 {device_id}: 开始自动评论 {count} 个视频")
    
    success_count = 0
    
    for i in range(count):
        try:
            log_info(f"设备 {device_id}: 正在评论第 {i+1} 个视频")
            
            # 等待视频加载
            time.sleep(DELAY_BETWEEN_VIDEOS)
            
            # 随机选择评论内容
            comment_text = random.choice(COMMENTS)
            log_info(f"设备 {device_id}: 准备评论: {comment_text}")
            
            # 点击评论按钮
            click_phone(device_id, COMMENT_BUTTON[0], COMMENT_BUTTON[1], "打开评论")
            time.sleep(DELAY_BETWEEN_ACTIONS)
            
            # 点击评论输入框
            click_phone(device_id, COMMENT_INPUT[0], COMMENT_INPUT[1], "点击输入框")
            time.sleep(1)
            
            # 输入评论内容
            input_text(device_id, comment_text)
            log_info(f"设备 {device_id}: 已输入评论内容")
            time.sleep(1)
            
            # 发送评论
            click_phone(device_id, SEND_BUTTON[0], SEND_BUTTON[1], "发送评论")
            log_info(f"设备 {device_id}: 已发送评论")
            time.sleep(DELAY_BETWEEN_ACTIONS)
            
            # 返回视频界面
            click_phone(device_id, BACK_BUTTON[0], BACK_BUTTON[1], "返回")
            time.sleep(DELAY_BETWEEN_ACTIONS)
            
            # 滑动到下一个视频 (除了最后一个)
            if i < count - 1:
                touch_swip(device_id, SWIPE_START[0], SWIPE_START[1], 
                          SWIPE_END[0], SWIPE_END[1], 200)
                log_info(f"设备 {device_id}: 已滑动到下一个视频")
            
            success_count += 1
            
        except Exception as e:
            log_error(f"设备 {device_id}: 评论第 {i+1} 个视频时出错: {str(e)}")
            # 尝试返回到视频界面
            try:
                click_phone(device_id, BACK_BUTTON[0], BACK_BUTTON[1], "返回")
                time.sleep(1)
            except:
                pass
            continue
    
    log_success(f"设备 {device_id}: 完成自动评论，成功评论 {success_count}/{count} 个视频")
    return success_count

# 主执行逻辑
try:
    # 首先打开抖音应用
    for device_id in device_ids:
        log_info(f"设备 {device_id}: 正在打开抖音应用")
        open_app(device_id, "com.ss.android.ugc.aweme")
    
    # 等待应用启动
    log_info("等待抖音应用启动...")
    time.sleep(5)
    
    # 对每个设备执行自动评论
    total_success = 0
    for device_id in device_ids:
        success = auto_comment_videos(device_id)
        total_success += success
    
    log_success(f"脚本执行完成！总共成功评论 {total_success} 个视频")
    
except Exception as e:
    log_error(f"脚本执行失败: {str(e)}")
    raise

log_info(f"{script_name} 执行结束")
