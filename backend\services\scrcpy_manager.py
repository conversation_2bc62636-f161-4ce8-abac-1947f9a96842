"""
scrcpy管理服务
管理scrcpy进程，实现设备画面的实时传输
"""

import asyncio
import subprocess
import logging
import os
import signal
from typing import Dict, Optional, List
import psutil
import socket

logger = logging.getLogger(__name__)

class ScrcpyManager:
    """scrcpy管理器"""
    
    def __init__(self):
        self.scrcpy_processes: Dict[str, subprocess.Popen] = {}
        self.device_ports: Dict[str, int] = {}
        self.base_port = 8080  # scrcpy服务的起始端口
        
    async def start_scrcpy(self, device_id: str, options: Optional[Dict] = None) -> bool:
        """启动scrcpy进程"""
        if device_id in self.scrcpy_processes:
            logger.warning(f"设备 {device_id} 的scrcpy已在运行")
            return True
        
        try:
            # 分配端口
            port = self._allocate_port()
            if not port:
                logger.error("无法分配可用端口")
                return False
            
            # 构建scrcpy命令
            cmd = self._build_scrcpy_command(device_id, port, options)
            
            logger.info(f"启动scrcpy进程: {' '.join(cmd)}")
            
            # 启动scrcpy进程
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                stdin=asyncio.subprocess.PIPE
            )
            
            # 等待一下确保进程启动
            await asyncio.sleep(2)
            
            # 检查进程是否还在运行
            if process.returncode is not None:
                # 进程已退出，读取错误信息
                stdout, stderr = await process.communicate()
                logger.error(f"scrcpy启动失败: {stderr.decode()}")
                self._release_port(port)
                return False
            
            # 保存进程信息
            self.scrcpy_processes[device_id] = process
            self.device_ports[device_id] = port
            
            logger.info(f"设备 {device_id} 的scrcpy已启动，端口: {port}")
            return True
            
        except Exception as e:
            logger.error(f"启动scrcpy时出错: {e}")
            if port:
                self._release_port(port)
            return False
    
    def _build_scrcpy_command(self, device_id: str, port: int, options: Optional[Dict] = None) -> List[str]:
        """构建scrcpy命令"""
        cmd = ['scrcpy']
        
        # 指定设备
        cmd.extend(['-s', device_id])
        
        # 无窗口模式 (用于服务器)
        cmd.append('--no-display')
        
        # 录制到stdout (用于流式传输)
        cmd.extend(['--record', '-'])
        
        # 设置比特率
        cmd.extend(['-b', '2M'])
        
        # 设置最大尺寸
        cmd.extend(['-m', '800'])
        
        # 设置帧率
        cmd.extend(['--max-fps', '30'])
        
        # 禁用音频
        cmd.append('--no-audio')
        
        # 应用自定义选项
        if options:
            if 'bitrate' in options:
                cmd.extend(['-b', str(options['bitrate'])])
            if 'max_size' in options:
                cmd.extend(['-m', str(options['max_size'])])
            if 'max_fps' in options:
                cmd.extend(['--max-fps', str(options['max_fps'])])
        
        return cmd
    
    def _allocate_port(self) -> Optional[int]:
        """分配可用端口"""
        for port in range(self.base_port, self.base_port + 100):
            if self._is_port_available(port) and port not in self.device_ports.values():
                return port
        return None
    
    def _is_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False
    
    def _release_port(self, port: int):
        """释放端口"""
        # 从设备端口映射中移除
        for device_id, device_port in list(self.device_ports.items()):
            if device_port == port:
                del self.device_ports[device_id]
                break
    
    async def stop_scrcpy(self, device_id: str) -> bool:
        """停止scrcpy进程"""
        if device_id not in self.scrcpy_processes:
            logger.warning(f"设备 {device_id} 的scrcpy未在运行")
            return True
        
        try:
            process = self.scrcpy_processes[device_id]
            
            # 优雅地终止进程
            if process.returncode is None:
                process.terminate()
                
                # 等待进程结束
                try:
                    await asyncio.wait_for(process.wait(), timeout=5.0)
                except asyncio.TimeoutError:
                    # 强制杀死进程
                    process.kill()
                    await process.wait()
            
            # 清理资源
            port = self.device_ports.get(device_id)
            if port:
                self._release_port(port)
            
            del self.scrcpy_processes[device_id]
            
            logger.info(f"设备 {device_id} 的scrcpy已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止scrcpy时出错: {e}")
            return False
    
    async def stop_all(self):
        """停止所有scrcpy进程"""
        logger.info("停止所有scrcpy进程...")
        
        tasks = []
        for device_id in list(self.scrcpy_processes.keys()):
            tasks.append(self.stop_scrcpy(device_id))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info("所有scrcpy进程已停止")
    
    def get_device_port(self, device_id: str) -> Optional[int]:
        """获取设备的scrcpy端口"""
        return self.device_ports.get(device_id)
    
    def is_running(self, device_id: str) -> bool:
        """检查设备的scrcpy是否在运行"""
        if device_id not in self.scrcpy_processes:
            return False
        
        process = self.scrcpy_processes[device_id]
        return process.returncode is None
    
    def get_running_devices(self) -> List[str]:
        """获取正在运行scrcpy的设备列表"""
        running_devices = []
        for device_id, process in self.scrcpy_processes.items():
            if process.returncode is None:
                running_devices.append(device_id)
        return running_devices
    
    async def restart_scrcpy(self, device_id: str, options: Optional[Dict] = None) -> bool:
        """重启scrcpy进程"""
        logger.info(f"重启设备 {device_id} 的scrcpy")
        
        # 先停止
        await self.stop_scrcpy(device_id)
        
        # 等待一下
        await asyncio.sleep(1)
        
        # 再启动
        return await self.start_scrcpy(device_id, options)
    
    async def get_scrcpy_status(self, device_id: str) -> Dict:
        """获取scrcpy状态信息"""
        status = {
            'device_id': device_id,
            'running': False,
            'port': None,
            'pid': None,
            'uptime': None
        }
        
        if device_id in self.scrcpy_processes:
            process = self.scrcpy_processes[device_id]
            status['running'] = process.returncode is None
            status['port'] = self.device_ports.get(device_id)
            status['pid'] = process.pid
            
            # 获取进程运行时间
            if status['running']:
                try:
                    ps_process = psutil.Process(process.pid)
                    status['uptime'] = ps_process.create_time()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
        
        return status
    
    async def check_scrcpy_health(self):
        """检查scrcpy进程健康状态"""
        unhealthy_devices = []
        
        for device_id, process in list(self.scrcpy_processes.items()):
            try:
                # 检查进程是否还在运行
                if process.returncode is not None:
                    logger.warning(f"设备 {device_id} 的scrcpy进程已退出")
                    unhealthy_devices.append(device_id)
                    continue
                
                # 检查进程是否响应
                try:
                    ps_process = psutil.Process(process.pid)
                    if ps_process.status() == psutil.STATUS_ZOMBIE:
                        logger.warning(f"设备 {device_id} 的scrcpy进程成为僵尸进程")
                        unhealthy_devices.append(device_id)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    logger.warning(f"设备 {device_id} 的scrcpy进程不存在")
                    unhealthy_devices.append(device_id)
                    
            except Exception as e:
                logger.error(f"检查设备 {device_id} scrcpy健康状态时出错: {e}")
                unhealthy_devices.append(device_id)
        
        # 清理不健康的进程
        for device_id in unhealthy_devices:
            await self.stop_scrcpy(device_id)
        
        return unhealthy_devices
