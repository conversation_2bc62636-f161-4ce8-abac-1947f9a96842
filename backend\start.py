#!/usr/bin/env python3
"""
启动脚本 - 启动设备群控系统后端服务
"""

import sys
import os
import subprocess
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_requirements():
    """检查依赖是否已安装"""
    try:
        import fastapi
        import uvicorn
        import pydantic
        import psutil
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_adb():
    """检查ADB是否可用"""
    try:
        result = subprocess.run(['adb', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ ADB可用")
            return True
        else:
            print("✗ ADB不可用")
            return False
    except FileNotFoundError:
        print("✗ 未找到ADB命令")
        print("请确保已安装Android SDK并将adb添加到系统PATH中")
        return False

def check_scrcpy():
    """检查scrcpy是否可用"""
    try:
        result = subprocess.run(['scrcpy', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ scrcpy可用")
            return True
        else:
            print("✗ scrcpy不可用")
            return False
    except FileNotFoundError:
        print("✗ 未找到scrcpy命令")
        print("请从 https://github.com/Genymobile/scrcpy 下载并安装scrcpy")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("设备群控系统后端服务启动检查")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        sys.exit(1)
    
    # 检查ADB
    if not check_adb():
        print("警告: ADB不可用，设备管理功能将无法正常工作")
    
    # 检查scrcpy
    if not check_scrcpy():
        print("警告: scrcpy不可用，设备画面显示功能将无法正常工作")
    
    print("\n" + "=" * 50)
    print("启动后端服务...")
    print("=" * 50)
    
    # 启动服务
    try:
        import uvicorn
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动服务失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
