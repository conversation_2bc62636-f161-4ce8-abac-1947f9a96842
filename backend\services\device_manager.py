"""
设备管理服务
集成ADB功能，管理Android设备的连接、状态监控等
"""

import asyncio
import subprocess
import re
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from models.device import Device, DeviceStatus, DeviceType, DeviceCommand, DeviceResponse
from models.device import TouchEvent, KeyEvent, TextInput, AppOperation

logger = logging.getLogger(__name__)

class DeviceManager:
    """设备管理器"""
    
    def __init__(self):
        self.devices: Dict[str, Device] = {}
        self.device_processes: Dict[str, subprocess.Popen] = {}
        
    async def initialize(self):
        """初始化设备管理器"""
        logger.info("初始化设备管理器...")
        
        # 检查ADB是否可用
        if not await self._check_adb_available():
            logger.error("ADB不可用，请确保已安装Android SDK并将adb添加到PATH")
            return False
        
        # 扫描初始设备
        await self.scan_devices()
        
        logger.info("设备管理器初始化完成")
        return True
    
    async def _check_adb_available(self) -> bool:
        """检查ADB是否可用"""
        try:
            result = await asyncio.create_subprocess_exec(
                'adb', 'version',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()
            return result.returncode == 0
        except FileNotFoundError:
            return False
        except Exception as e:
            logger.error(f"检查ADB可用性时出错: {e}")
            return False
    
    async def scan_devices(self) -> List[Device]:
        """扫描ADB设备"""
        logger.info("扫描ADB设备...")
        
        try:
            # 执行adb devices命令
            result = await asyncio.create_subprocess_exec(
                'adb', 'devices', '-l',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()
            
            if result.returncode != 0:
                logger.error(f"ADB devices命令失败: {stderr.decode()}")
                return []
            
            # 解析设备列表
            output = stdout.decode()
            devices = []
            
            for line in output.split('\n'):
                line = line.strip()
                if not line or line.startswith('List of devices'):
                    continue
                
                # 解析设备信息
                match = re.match(r'([\w\d:.]+)\s+(device|offline|unauthorized)', line)
                if match:
                    device_id = match.group(1)
                    status_str = match.group(2)
                    
                    # 转换状态
                    if status_str == 'device':
                        status = DeviceStatus.ONLINE
                    elif status_str == 'offline':
                        status = DeviceStatus.OFFLINE
                    else:
                        status = DeviceStatus.ERROR
                    
                    # 获取或创建设备对象
                    if device_id in self.devices:
                        device = self.devices[device_id]
                        device.status = status
                        device.last_seen = datetime.now()
                    else:
                        device = Device(
                            id=device_id,
                            status=status,
                            device_type=DeviceType.ANDROID,
                            last_seen=datetime.now()
                        )
                        self.devices[device_id] = device
                    
                    # 如果设备在线，获取详细信息
                    if status == DeviceStatus.ONLINE:
                        await self._get_device_info(device)
                    
                    devices.append(device)
            
            logger.info(f"扫描到 {len(devices)} 个设备")
            return devices
            
        except Exception as e:
            logger.error(f"扫描设备时出错: {e}")
            return []
    
    async def _get_device_info(self, device: Device):
        """获取设备详细信息"""
        try:
            # 获取设备属性
            props = await self._get_device_properties(device.id)
            
            # 更新设备信息
            device.name = props.get('ro.product.model', device.id)
            device.model = props.get('ro.product.model')
            device.android_version = props.get('ro.build.version.release')
            
            # 获取屏幕信息
            resolution = await self._get_screen_resolution(device.id)
            if resolution:
                device.resolution = resolution
            
            # 获取电池信息
            battery = await self._get_battery_level(device.id)
            if battery:
                device.battery_level = battery
                
        except Exception as e:
            logger.error(f"获取设备 {device.id} 信息时出错: {e}")
    
    async def _get_device_properties(self, device_id: str) -> Dict[str, str]:
        """获取设备属性"""
        try:
            result = await asyncio.create_subprocess_exec(
                'adb', '-s', device_id, 'shell', 'getprop',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()
            
            if result.returncode != 0:
                return {}
            
            props = {}
            for line in stdout.decode().split('\n'):
                match = re.match(r'\[(.*?)\]: \[(.*?)\]', line.strip())
                if match:
                    key, value = match.groups()
                    props[key] = value
            
            return props
            
        except Exception as e:
            logger.error(f"获取设备属性时出错: {e}")
            return {}
    
    async def _get_screen_resolution(self, device_id: str) -> Optional[str]:
        """获取屏幕分辨率"""
        try:
            result = await asyncio.create_subprocess_exec(
                'adb', '-s', device_id, 'shell', 'wm', 'size',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                output = stdout.decode().strip()
                match = re.search(r'(\d+x\d+)', output)
                if match:
                    return match.group(1)
            
            return None
            
        except Exception as e:
            logger.error(f"获取屏幕分辨率时出错: {e}")
            return None
    
    async def _get_battery_level(self, device_id: str) -> Optional[int]:
        """获取电池电量"""
        try:
            result = await asyncio.create_subprocess_exec(
                'adb', '-s', device_id, 'shell', 'dumpsys', 'battery',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                output = stdout.decode()
                match = re.search(r'level: (\d+)', output)
                if match:
                    return int(match.group(1))
            
            return None
            
        except Exception as e:
            logger.error(f"获取电池电量时出错: {e}")
            return None
    
    async def get_all_devices(self) -> List[Device]:
        """获取所有设备"""
        return list(self.devices.values())
    
    async def get_device(self, device_id: str) -> Optional[Device]:
        """获取指定设备"""
        return self.devices.get(device_id)
    
    async def connect_device(self, device_id: str) -> bool:
        """连接设备"""
        device = self.devices.get(device_id)
        if not device:
            logger.error(f"设备 {device_id} 不存在")
            return False
        
        try:
            device.status = DeviceStatus.CONNECTING
            
            # 如果是网络设备，尝试连接
            if ':' in device_id:
                result = await asyncio.create_subprocess_exec(
                    'adb', 'connect', device_id,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()
                
                if result.returncode != 0:
                    device.status = DeviceStatus.ERROR
                    logger.error(f"连接设备 {device_id} 失败: {stderr.decode()}")
                    return False
            
            # 更新设备状态
            device.status = DeviceStatus.ONLINE
            device.connected_at = datetime.now()
            device.last_seen = datetime.now()
            
            # 获取设备详细信息
            await self._get_device_info(device)
            
            logger.info(f"设备 {device_id} 连接成功")
            return True
            
        except Exception as e:
            device.status = DeviceStatus.ERROR
            logger.error(f"连接设备 {device_id} 时出错: {e}")
            return False
    
    async def disconnect_device(self, device_id: str) -> bool:
        """断开设备连接"""
        device = self.devices.get(device_id)
        if not device:
            logger.error(f"设备 {device_id} 不存在")
            return False
        
        try:
            device.status = DeviceStatus.DISCONNECTING
            
            # 如果是网络设备，断开连接
            if ':' in device_id:
                result = await asyncio.create_subprocess_exec(
                    'adb', 'disconnect', device_id,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await result.communicate()
            
            # 更新设备状态
            device.status = DeviceStatus.OFFLINE
            device.connected_at = None
            device.scrcpy_running = False
            device.scrcpy_port = None
            
            logger.info(f"设备 {device_id} 断开连接")
            return True
            
        except Exception as e:
            device.status = DeviceStatus.ERROR
            logger.error(f"断开设备 {device_id} 连接时出错: {e}")
            return False
    
    async def check_device_status(self) -> List[Device]:
        """检查设备状态"""
        changed_devices = []

        try:
            # 重新扫描设备
            current_devices = await self.scan_devices()

            # 检查状态变化
            for device in current_devices:
                old_device = self.devices.get(device.id)
                if old_device and old_device.status != device.status:
                    changed_devices.append(device)

            return changed_devices

        except Exception as e:
            logger.error(f"检查设备状态时出错: {e}")
            return []

    # 设备控制功能 (集成原有的douyin_adb.py功能)

    async def execute_touch(self, touch_event: TouchEvent) -> DeviceResponse:
        """执行触摸操作"""
        device = self.devices.get(touch_event.device_id)
        if not device or device.status != DeviceStatus.ONLINE:
            return DeviceResponse(
                device_id=touch_event.device_id,
                success=False,
                message="设备不在线"
            )

        try:
            if touch_event.action == "tap":
                # 点击操作
                result = await asyncio.create_subprocess_exec(
                    'adb', '-s', touch_event.device_id, 'shell', 'input', 'tap',
                    str(touch_event.x), str(touch_event.y),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                success = result.returncode == 0
                message = f"点击坐标 ({touch_event.x}, {touch_event.y})" + ("成功" if success else f"失败: {stderr.decode()}")

            elif touch_event.action == "swipe":
                # 滑动操作
                duration = touch_event.duration or 300
                result = await asyncio.create_subprocess_exec(
                    'adb', '-s', touch_event.device_id, 'shell', 'input', 'swipe',
                    str(touch_event.x), str(touch_event.y),
                    str(touch_event.end_x), str(touch_event.end_y),
                    str(duration),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                success = result.returncode == 0
                message = f"滑动从 ({touch_event.x}, {touch_event.y}) 到 ({touch_event.end_x}, {touch_event.end_y})" + ("成功" if success else f"失败: {stderr.decode()}")

            elif touch_event.action == "long_press":
                # 长按操作 (通过swipe实现，起点终点相同)
                duration = touch_event.duration or 1000
                result = await asyncio.create_subprocess_exec(
                    'adb', '-s', touch_event.device_id, 'shell', 'input', 'swipe',
                    str(touch_event.x), str(touch_event.y),
                    str(touch_event.x), str(touch_event.y),
                    str(duration),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                success = result.returncode == 0
                message = f"长按坐标 ({touch_event.x}, {touch_event.y})" + ("成功" if success else f"失败: {stderr.decode()}")

            else:
                return DeviceResponse(
                    device_id=touch_event.device_id,
                    success=False,
                    message=f"不支持的触摸操作: {touch_event.action}"
                )

            return DeviceResponse(
                device_id=touch_event.device_id,
                success=success,
                message=message
            )

        except Exception as e:
            logger.error(f"执行触摸操作时出错: {e}")
            return DeviceResponse(
                device_id=touch_event.device_id,
                success=False,
                message=f"执行触摸操作时出错: {str(e)}"
            )

    async def execute_key(self, key_event: KeyEvent) -> DeviceResponse:
        """执行按键操作"""
        device = self.devices.get(key_event.device_id)
        if not device or device.status != DeviceStatus.ONLINE:
            return DeviceResponse(
                device_id=key_event.device_id,
                success=False,
                message="设备不在线"
            )

        try:
            result = await asyncio.create_subprocess_exec(
                'adb', '-s', key_event.device_id, 'shell', 'input', 'keyevent',
                key_event.key_code,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            success = result.returncode == 0
            message = f"按键 {key_event.key_code}" + ("成功" if success else f"失败: {stderr.decode()}")

            return DeviceResponse(
                device_id=key_event.device_id,
                success=success,
                message=message
            )

        except Exception as e:
            logger.error(f"执行按键操作时出错: {e}")
            return DeviceResponse(
                device_id=key_event.device_id,
                success=False,
                message=f"执行按键操作时出错: {str(e)}"
            )

    async def input_text(self, text_input: TextInput) -> DeviceResponse:
        """输入文本"""
        device = self.devices.get(text_input.device_id)
        if not device or device.status != DeviceStatus.ONLINE:
            return DeviceResponse(
                device_id=text_input.device_id,
                success=False,
                message="设备不在线"
            )

        try:
            # 如果需要清空输入框
            if text_input.clear_before:
                # 全选并删除
                await asyncio.create_subprocess_exec(
                    'adb', '-s', text_input.device_id, 'shell', 'input', 'keyevent', 'KEYCODE_CTRL_A'
                )
                await asyncio.create_subprocess_exec(
                    'adb', '-s', text_input.device_id, 'shell', 'input', 'keyevent', 'KEYCODE_DEL'
                )

            # 转义特殊字符
            escaped_text = self._escape_text(text_input.text)

            result = await asyncio.create_subprocess_exec(
                'adb', '-s', text_input.device_id, 'shell', 'input', 'text', escaped_text,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            success = result.returncode == 0
            message = f"输入文本 '{text_input.text}'" + ("成功" if success else f"失败: {stderr.decode()}")

            return DeviceResponse(
                device_id=text_input.device_id,
                success=success,
                message=message
            )

        except Exception as e:
            logger.error(f"输入文本时出错: {e}")
            return DeviceResponse(
                device_id=text_input.device_id,
                success=False,
                message=f"输入文本时出错: {str(e)}"
            )

    def _escape_text(self, text: str) -> str:
        """转义文本中的特殊字符"""
        escaped_text = ''
        for char in text:
            if char == ' ':
                escaped_text += '%s'
            elif char in '"\'+*[](){}^|\\&;`~<>':
                escaped_text += '\\' + char
            else:
                escaped_text += char
        return escaped_text

    async def manage_app(self, app_operation: AppOperation) -> DeviceResponse:
        """应用管理操作"""
        device = self.devices.get(app_operation.device_id)
        if not device or device.status != DeviceStatus.ONLINE:
            return DeviceResponse(
                device_id=app_operation.device_id,
                success=False,
                message="设备不在线"
            )

        try:
            if app_operation.action == "start":
                # 启动应用
                result = await asyncio.create_subprocess_exec(
                    'adb', '-s', app_operation.device_id, 'shell', 'monkey', '-p',
                    app_operation.package_name, '-c', 'android.intent.category.LAUNCHER', '1',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                success = result.returncode == 0
                message = f"启动应用 {app_operation.package_name}" + ("成功" if success else f"失败: {stderr.decode()}")

            elif app_operation.action == "stop":
                # 停止应用
                result = await asyncio.create_subprocess_exec(
                    'adb', '-s', app_operation.device_id, 'shell', 'am', 'force-stop',
                    app_operation.package_name,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                success = result.returncode == 0
                message = f"停止应用 {app_operation.package_name}" + ("成功" if success else f"失败: {stderr.decode()}")

            elif app_operation.action == "install":
                # 安装应用
                if not app_operation.apk_path:
                    return DeviceResponse(
                        device_id=app_operation.device_id,
                        success=False,
                        message="安装应用需要提供APK路径"
                    )

                result = await asyncio.create_subprocess_exec(
                    'adb', '-s', app_operation.device_id, 'install', app_operation.apk_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                success = result.returncode == 0
                message = f"安装应用 {app_operation.apk_path}" + ("成功" if success else f"失败: {stderr.decode()}")

            elif app_operation.action == "uninstall":
                # 卸载应用
                result = await asyncio.create_subprocess_exec(
                    'adb', '-s', app_operation.device_id, 'uninstall', app_operation.package_name,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                success = result.returncode == 0
                message = f"卸载应用 {app_operation.package_name}" + ("成功" if success else f"失败: {stderr.decode()}")

            elif app_operation.action == "clear_data":
                # 清除应用数据
                result = await asyncio.create_subprocess_exec(
                    'adb', '-s', app_operation.device_id, 'shell', 'pm', 'clear',
                    app_operation.package_name,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                success = result.returncode == 0
                message = f"清除应用数据 {app_operation.package_name}" + ("成功" if success else f"失败: {stderr.decode()}")

            else:
                return DeviceResponse(
                    device_id=app_operation.device_id,
                    success=False,
                    message=f"不支持的应用操作: {app_operation.action}"
                )

            return DeviceResponse(
                device_id=app_operation.device_id,
                success=success,
                message=message
            )

        except Exception as e:
            logger.error(f"执行应用操作时出错: {e}")
            return DeviceResponse(
                device_id=app_operation.device_id,
                success=False,
                message=f"执行应用操作时出错: {str(e)}"
            )

    async def get_installed_apps(self, device_id: str) -> DeviceResponse:
        """获取已安装应用列表"""
        device = self.devices.get(device_id)
        if not device or device.status != DeviceStatus.ONLINE:
            return DeviceResponse(
                device_id=device_id,
                success=False,
                message="设备不在线"
            )

        try:
            result = await asyncio.create_subprocess_exec(
                'adb', '-s', device_id, 'shell', 'pm', 'list', 'packages',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                packages = []
                for line in stdout.decode().split('\n'):
                    if line.startswith('package:'):
                        package_name = line.replace('package:', '').strip()
                        packages.append(package_name)

                return DeviceResponse(
                    device_id=device_id,
                    success=True,
                    message=f"获取到 {len(packages)} 个应用",
                    data={"packages": packages}
                )
            else:
                return DeviceResponse(
                    device_id=device_id,
                    success=False,
                    message=f"获取应用列表失败: {stderr.decode()}"
                )

        except Exception as e:
            logger.error(f"获取应用列表时出错: {e}")
            return DeviceResponse(
                device_id=device_id,
                success=False,
                message=f"获取应用列表时出错: {str(e)}"
            )

    async def take_screenshot(self, device_id: str, save_path: Optional[str] = None) -> DeviceResponse:
        """截屏"""
        device = self.devices.get(device_id)
        if not device or device.status != DeviceStatus.ONLINE:
            return DeviceResponse(
                device_id=device_id,
                success=False,
                message="设备不在线"
            )

        try:
            # 设备上截屏
            result = await asyncio.create_subprocess_exec(
                'adb', '-s', device_id, 'shell', 'screencap', '/sdcard/screenshot.png',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode != 0:
                return DeviceResponse(
                    device_id=device_id,
                    success=False,
                    message=f"设备截屏失败: {stderr.decode()}"
                )

            # 拉取截屏文件
            if save_path:
                result = await asyncio.create_subprocess_exec(
                    'adb', '-s', device_id, 'pull', '/sdcard/screenshot.png', save_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                if result.returncode == 0:
                    # 删除设备上的临时文件
                    await asyncio.create_subprocess_exec(
                        'adb', '-s', device_id, 'shell', 'rm', '/sdcard/screenshot.png'
                    )

                    return DeviceResponse(
                        device_id=device_id,
                        success=True,
                        message=f"截屏保存到: {save_path}",
                        data={"screenshot_path": save_path}
                    )
                else:
                    return DeviceResponse(
                        device_id=device_id,
                        success=False,
                        message=f"拉取截屏文件失败: {stderr.decode()}"
                    )
            else:
                return DeviceResponse(
                    device_id=device_id,
                    success=True,
                    message="截屏完成，文件保存在设备 /sdcard/screenshot.png"
                )

        except Exception as e:
            logger.error(f"截屏时出错: {e}")
            return DeviceResponse(
                device_id=device_id,
                success=False,
                message=f"截屏时出错: {str(e)}"
            )
