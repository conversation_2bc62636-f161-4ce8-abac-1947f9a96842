<template>
  <div class="app-container">
    <!-- 顶部菜单栏 -->
    <header class="top-menu">
      <div class="menu-left">
        <div class="menu-title">
          <el-icon class="title-icon"><Monitor /></el-icon>
          设备群控系统
          <el-tag size="small" type="success" class="version-tag">v1.0</el-tag>
        </div>
        <div class="system-stats">
          <el-statistic
            title="在线设备"
            :value="onlineDeviceCount"
            class="stat-item"
          >
            <template #suffix>
              <el-icon><Connection /></el-icon>
            </template>
          </el-statistic>
          <el-statistic
            title="运行脚本"
            :value="runningScriptCount"
            class="stat-item"
          >
            <template #suffix>
              <el-icon><VideoPlay /></el-icon>
            </template>
          </el-statistic>
        </div>
      </div>
      <div class="menu-center">
        <el-radio-group v-model="currentView" class="view-selector">
          <el-radio-button value="deviceView">
            <el-icon><Monitor /></el-icon>
            设备视图
          </el-radio-button>
          <el-radio-button value="scriptView">
            <el-icon><Document /></el-icon>
            脚本管理
          </el-radio-button>
        </el-radio-group>
      </div>
      <div class="menu-options">
        <el-tooltip content="系统状态" placement="bottom">
          <el-badge :value="systemStatus === 'healthy' ? '' : '!'" :type="systemStatus === 'healthy' ? 'success' : 'danger'">
            <el-button
              :type="systemStatus === 'healthy' ? 'success' : 'warning'"
              circle
              size="small"
              @click="checkSystemStatus"
            >
              <el-icon><Connection /></el-icon>
            </el-button>
          </el-badge>
        </el-tooltip>
        <el-tooltip content="刷新所有数据" placement="bottom">
          <el-button @click="refreshAll" :loading="refreshing" circle>
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="系统设置" placement="bottom">
          <el-button @click="showSettings" circle>
            <el-icon><Setting /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 设备视图 -->
      <template v-if="currentView === 'deviceView'">
        <!-- 左侧设备管理 -->
        <aside class="left-panel">
          <DeviceManager v-model:devices="allDevices" />
        </aside>

        <!-- 中间设备展示 -->
        <section class="center-panel">
          <DeviceDisplay :devices="allDevices" />
        </section>

        <!-- 右侧脚本日志 -->
        <aside class="right-panel">
          <ScriptLog />
        </aside>
      </template>

      <!-- 脚本管理视图 -->
      <template v-else-if="currentView === 'scriptView'">
        <section class="script-manager-container">
          <ScriptManager />
        </section>
      </template>
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import { Monitor, Connection, VideoPlay, Document, Refresh, Setting } from '@element-plus/icons-vue';
import DeviceManager from './components/DeviceManager.vue';
import DeviceDisplay from './components/DeviceDisplay.vue';
import ScriptLog from './components/ScriptLog.vue';
import ScriptManager from './components/ScriptManager.vue';

export default {
  name: 'App',
  components: {
    DeviceManager,
    DeviceDisplay,
    ScriptLog,
    ScriptManager,
    Monitor,
    Connection,
    VideoPlay,
    Document,
    Refresh,
    Setting
  },
  setup() {
    const currentView = ref('deviceView');
    const systemStatus = ref('healthy');
    const refreshing = ref(false);

    // 共享的设备数据
    const allDevices = ref([
      { id: 'JEF-AN00', name: '华为手机', status: 'online' },
      { id: 'SM-G998B', name: '三星手机', status: 'online' },
      { id: 'iPhone13', name: '苹果手机', status: 'offline' },
      { id: 'MI12', name: '小米手机', status: 'online' },
      { id: 'OPPO-Reno7', name: 'OPPO手机', status: 'online' },
      { id: 'VIVO-X70', name: 'VIVO手机', status: 'offline' }
    ]);

    // 计算属性
    const onlineDeviceCount = computed(() => {
      return allDevices.value.filter(device => device.status === 'online').length;
    });

    const runningScriptCount = computed(() => {
      // 这里应该从脚本管理器获取运行中的脚本数量
      return 0; // 临时值
    });

    // 方法
    const switchView = (view) => {
      currentView.value = view;
      ElMessage.success(`已切换到${view === 'deviceView' ? '设备视图' : '脚本管理'}`);
    };

    const refreshAll = async () => {
      refreshing.value = true;
      try {
        // 刷新设备列表
        // 刷新脚本状态
        // 刷新系统状态
        await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟刷新
        ElMessage.success('数据刷新完成');
      } catch (error) {
        ElMessage.error('刷新失败: ' + error.message);
      } finally {
        refreshing.value = false;
      }
    };

    const checkSystemStatus = async () => {
      try {
        // 检查后端连接状态
        const response = await fetch('http://localhost:8000/api/health');
        if (response.ok) {
          systemStatus.value = 'healthy';
        } else {
          systemStatus.value = 'warning';
        }
      } catch (error) {
        systemStatus.value = 'error';
      }
    };

    const showSettings = () => {
      ElMessage.info('设置功能开发中...');
    };

    // 生命周期
    onMounted(() => {
      checkSystemStatus();
      // 定期检查系统状态
      setInterval(checkSystemStatus, 30000); // 每30秒检查一次
    });

    return {
      currentView,
      systemStatus,
      refreshing,
      onlineDeviceCount,
      runningScriptCount,
      allDevices,
      switchView,
      refreshAll,
      checkSystemStatus,
      showSettings
    };
  }
};
</script>

<style scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.top-menu {
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 1000;
  position: relative;
}

.menu-left {
  display: flex;
  align-items: center;
  gap: 30px;
}

.menu-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 24px;
}

.version-tag {
  margin-left: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.system-stats {
  display: flex;
  gap: 20px;
  align-items: center;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  backdrop-filter: blur(10px);
  min-width: 80px;
  text-align: center;
}

.stat-item :deep(.el-statistic__content) {
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.stat-item :deep(.el-statistic__head) {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-bottom: 4px;
}

.menu-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.view-selector {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 4px;
}

.view-selector :deep(.el-radio-button__inner) {
  background: transparent;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.view-selector :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.menu-options {
  display: flex;
  gap: 10px;
  align-items: center;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  width: 250px;
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.center-panel {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.right-panel {
  width: 300px;
  background-color: #fff;
  border-left: 1px solid #e4e7ed;
  overflow-y: auto;
}

.script-manager-container {
  flex: 1;
  overflow: hidden;
}
</style>