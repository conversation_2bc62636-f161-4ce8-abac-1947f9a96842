# 设备群控系统

基于ADB和scrcpy的Android设备群控系统，支持多设备管理、脚本自动化执行和实时画面显示。

## 功能特性

- 🔧 **设备管理**: 自动扫描和管理多个Android设备
- 📱 **实时画面**: 通过scrcpy实时显示设备画面
- 🤖 **脚本自动化**: 支持Python脚本编写和执行
- 🎯 **抖音自动化**: 内置抖音相关操作（点赞、收藏、关注、评论等）
- 🌐 **Web界面**: 现代化的Web管理界面
- 📊 **实时监控**: WebSocket实时通信和状态监控

## 系统架构

```
├── backend/           # 后端服务 (FastAPI + WebSocket)
│   ├── main.py       # 主服务器
│   ├── models/       # 数据模型
│   ├── services/     # 业务服务
│   └── utils/        # 工具模块
├── frontend/         # 前端界面 (Vue 3 + Element Plus)
│   └── src/
│       ├── components/  # Vue组件
│       └── utils/       # 工具函数
└── script/           # 脚本目录
    └── douyin_adb.py # 抖音自动化脚本
```

## 环境要求

### 必需组件
- Python 3.8+
- Node.js 16+
- Android SDK (ADB)
- scrcpy

### Python依赖
```bash
pip install -r backend/requirements.txt
```

### 前端依赖
```bash
cd frontend
npm install
```

## 安装步骤

### 1. 安装Android SDK
下载并安装Android SDK，确保`adb`命令可用：
```bash
adb version
```

### 2. 安装scrcpy
从 [scrcpy官网](https://github.com/Genymobile/scrcpy) 下载并安装scrcpy：
```bash
scrcpy --version
```

### 3. 安装项目依赖
```bash
# 安装后端依赖
cd backend
pip install -r requirements.txt

# 安装前端依赖
cd ../frontend
npm install
```

## 使用方法

### 1. 启动后端服务
```bash
cd backend
python start.py
```

后端服务将在 `http://localhost:8000` 启动

### 2. 启动前端服务
```bash
cd frontend
npm run dev
```

前端界面将在 `http://localhost:5173` 启动

### 3. 连接设备
1. 确保Android设备已开启USB调试
2. 通过USB连接设备到电脑
3. 在Web界面中点击"扫描设备"
4. 选择设备并点击"连接"

### 4. 使用脚本
1. 在"脚本管理"页面创建或编辑脚本
2. 选择要运行脚本的设备
3. 点击"运行"开始执行

## API接口

### 设备管理
- `GET /api/devices` - 获取设备列表
- `POST /api/devices/scan` - 扫描设备
- `POST /api/devices/{device_id}/connect` - 连接设备
- `POST /api/devices/{device_id}/disconnect` - 断开设备

### 设备控制
- `POST /api/devices/{device_id}/touch` - 触摸操作
- `POST /api/devices/{device_id}/key` - 按键操作
- `POST /api/devices/{device_id}/input` - 文本输入
- `POST /api/devices/{device_id}/app` - 应用管理

### 脚本管理
- `GET /api/scripts` - 获取脚本列表
- `POST /api/scripts` - 创建脚本
- `PUT /api/scripts/{script_id}` - 更新脚本
- `DELETE /api/scripts/{script_id}` - 删除脚本
- `POST /api/scripts/{script_id}/run` - 运行脚本

## 脚本编写

### 基本脚本结构
```python
# 脚本示例：抖音自动点赞
import time
import random

# 获取设备列表
for device_id in device_ids:
    log_info(f"开始在设备 {device_id} 上执行脚本")
    
    # 打开抖音
    open_app(device_id, "com.ss.android.ugc.aweme")
    time.sleep(3)
    
    # 循环点赞
    for i in range(10):
        # 点赞
        click_phone(device_id, 664, 900, "点赞")
        time.sleep(2)
        
        # 滑动到下一个视频
        touch_swip(device_id, 528, 800, 528, 410, 200)
        time.sleep(3)
    
    log_success(f"设备 {device_id} 脚本执行完成")
```

### 可用函数
- `click_phone(device_id, x, y, action_name)` - 点击屏幕
- `touch_swip(device_id, x1, y1, x2, y2, duration)` - 滑动操作
- `input_text(device_id, text)` - 输入文本
- `open_app(device_id, package_name)` - 打开应用
- `log_info(message)` - 记录信息日志
- `log_success(message)` - 记录成功日志
- `log_warning(message)` - 记录警告日志
- `log_error(message)` - 记录错误日志

## 常见问题

### 1. ADB设备未找到
- 确保设备已开启USB调试
- 检查USB连接
- 运行 `adb devices` 确认设备可见

### 2. scrcpy无法启动
- 确保scrcpy已正确安装
- 检查设备是否支持scrcpy
- 尝试手动运行 `scrcpy -s <device_id>`

### 3. 脚本执行失败
- 检查脚本语法是否正确
- 确保设备处于在线状态
- 查看日志了解具体错误信息

## 开发说明

### 后端开发
后端使用FastAPI框架，支持：
- RESTful API
- WebSocket实时通信
- 异步处理
- 自动API文档

### 前端开发
前端使用Vue 3 + Element Plus，特性：
- 响应式设计
- 组件化开发
- 实时数据更新
- 现代化UI

## 新增功能

### 🎨 增强的用户界面
- 现代化的响应式设计
- 实时系统状态监控
- 设备性能仪表板
- 智能脚本编辑器（代码高亮、自动补全、语法检查）

### 📊 设备性能监控
- CPU、内存、电池、存储使用率监控
- 实时性能图表
- 性能告警机制
- 设备温度监控

### 🛒 脚本模板市场
- 丰富的脚本模板库
- 模板分类和搜索
- 一键下载和使用
- 社区分享功能

### 🔒 安全管理
- 用户认证和授权
- JWT令牌认证
- API密钥管理
- 权限控制系统
- 登录尝试监控

### ⚡ 性能优化
- 资源池管理
- 自动重试机制
- 内存管理优化
- 异步任务调度

## 部署指南

### Docker部署（推荐）

1. **构建镜像**
```bash
# 构建后端镜像
cd backend
docker build -t device-control-backend .

# 构建前端镜像
cd ../frontend
docker build -t device-control-frontend .
```

2. **使用Docker Compose**
```bash
docker-compose up -d
```

### 手动部署

1. **环境准备**
```bash
# 安装Python 3.8+
python --version

# 安装Node.js 16+
node --version

# 安装ADB
adb version

# 安装scrcpy
scrcpy --version
```

2. **后端部署**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python start.py
```

3. **前端部署**
```bash
cd frontend
npm install
npm run build
npm run preview  # 或使用nginx等web服务器
```

### 生产环境配置

1. **环境变量配置**
```bash
# .env文件
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
LOG_LEVEL=info
JWT_SECRET=your-secret-key
DATABASE_URL=sqlite:///./data/app.db
```

2. **Nginx配置**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /ws {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 监控和维护

### 系统监控
- 访问 `/api/health` 检查系统健康状态
- 查看 `logs/` 目录下的日志文件
- 监控设备连接状态和性能指标

### 数据备份
```bash
# 备份脚本和配置
tar -czf backup-$(date +%Y%m%d).tar.gz data/ scripts/ config.json

# 定期清理日志
find logs/ -name "*.log" -mtime +7 -delete
```

### 故障排除

1. **设备连接问题**
```bash
# 检查ADB连接
adb devices

# 重启ADB服务
adb kill-server
adb start-server
```

2. **scrcpy问题**
```bash
# 检查scrcpy版本
scrcpy --version

# 手动测试设备连接
scrcpy -s device_id
```

3. **性能问题**
- 检查系统资源使用情况
- 调整并发设备数量
- 优化脚本执行频率

## API文档

系统启动后访问 `http://localhost:8000/docs` 查看完整的API文档。

### 主要API端点

- `POST /api/auth/login` - 用户登录
- `GET /api/devices` - 获取设备列表
- `POST /api/devices/scan` - 扫描设备
- `POST /api/scripts` - 创建脚本
- `POST /api/scripts/{id}/run` - 运行脚本
- `GET /api/health` - 健康检查

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

### 开发指南

1. **代码规范**
- Python: 遵循PEP 8
- JavaScript: 使用ESLint
- 提交信息: 使用约定式提交

2. **测试**
```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm run test
```

3. **文档**
- 更新API文档
- 添加功能说明
- 提供使用示例
